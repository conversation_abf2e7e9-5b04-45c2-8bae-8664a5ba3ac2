import { SEOProps } from '@/components/SEO';

export interface RouteConfig {
  path: string;
  seo: Partial<SEOProps>;
  isDynamic?: boolean;
}

// Default SEO configuration for static routes
export const routeConfigs: RouteConfig[] = [
  {
    path: '/',
    seo: {
      title: 'Startup Stories | Tech Entrepreneur Success Stories',
      description: 'Learn from successful entrepreneurs who\'ve built & scaled tech businesses. Join our community of tech founders sharing real stories of challenges and triumphs.',
      keywords: 'entrepreneur stories, startup success, tech founders, business stories, startup journey, founder interviews',
      type: 'website'
    }
  },
  {
    path: '/stories',
    seo: {
      title: 'Entrepreneur Stories | Startup Stories',
      description: 'Browse inspiring stories from successful tech entrepreneurs and startup founders. Learn from their challenges, failures, and triumphs.',
      keywords: 'entrepreneur stories, startup stories, founder journeys, business success stories',
      type: 'website'
    }
  },
  {
    path: '/blog',
    seo: {
      title: 'Startup Blog | Startup Stories',
      description: 'Read the latest insights, tips, and advice for entrepreneurs and startup founders. Expert content to help you build and scale your business.',
      keywords: 'startup blog, entrepreneur advice, business tips, startup insights',
      type: 'website'
    }
  },
  {
    path: '/categories',
    seo: {
      title: 'Story Categories | Startup Stories',
      description: 'Explore entrepreneur stories organized by industry, stage, and topic. Find relevant insights for your startup journey.',
      keywords: 'story categories, startup industries, entrepreneur topics',
      type: 'website'
    }
  },
  {
    path: '/directory',
    seo: {
      title: 'Founder Directory | Startup Stories',
      description: 'Connect with successful entrepreneurs and startup founders. Browse profiles of tech leaders sharing their stories.',
      keywords: 'founder directory, entrepreneur network, startup founders',
      type: 'website'
    }
  },
  {
    path: '/academy',
    seo: {
      title: 'Startup Academy | Startup Stories',
      description: 'Learn from expert courses and resources designed for entrepreneurs. Build the skills you need to succeed.',
      keywords: 'startup academy, entrepreneur education, business courses',
      type: 'website'
    }
  },
  {
    path: '/subscription-plans',
    seo: {
      title: 'Premium Plans | Startup Stories',
      description: 'Unlock premium entrepreneur stories and exclusive content. Join our community of successful founders.',
      keywords: 'premium stories, entrepreneur membership, startup community',
      type: 'website'
    }
  },
  {
    path: '/privacy-policy',
    seo: {
      title: 'Privacy Policy | Startup Stories',
      description: 'Learn how Startup Stories protects your privacy and handles your personal information.',
      keywords: 'privacy policy, data protection, user privacy',
      type: 'website',
      noIndex: true
    }
  },
  {
    path: '/terms-of-service',
    seo: {
      title: 'Terms of Service | Startup Stories',
      description: 'Read our terms of service and user agreement for using Startup Stories platform.',
      keywords: 'terms of service, user agreement, platform rules',
      type: 'website',
      noIndex: true
    }
  },
  {
    path: '/cookie-policy',
    seo: {
      title: 'Cookie Policy | Startup Stories',
      description: 'Learn about how we use cookies to improve your experience on Startup Stories.',
      keywords: 'cookie policy, website cookies, user experience',
      type: 'website',
      noIndex: true
    }
  }
];

// Dynamic route patterns
export const dynamicRoutes = {
  story: '/story/:storyId',
  blog: '/blog/:blogId',
  author: '/author/:authorId',
  authorUsername: '/author/@:username',
  category: '/category/:categoryId'
};

// Default fallback SEO
export const defaultSEO: SEOProps = {
  title: 'Startup Stories | Tech Entrepreneur Success Stories',
  description: 'Learn from successful entrepreneurs who\'ve built & scaled tech businesses. Join our community of tech founders sharing real stories of challenges and triumphs.',
  keywords: 'entrepreneur stories, startup success, tech founders, business stories, startup journey, founder interviews',
  image: 'https://lovable.dev/opengraph-image-p98pqg.png',
  type: 'website'
};

// Helper function to get SEO config for a route
export const getSEOConfigForRoute = (pathname: string): Partial<SEOProps> => {
  const config = routeConfigs.find(route => route.path === pathname);
  return config?.seo || {};
};

// Helper function to check if a route is dynamic
export const isDynamicRoute = (pathname: string): boolean => {
  return Object.values(dynamicRoutes).some(pattern => {
    const regex = new RegExp(pattern.replace(/:[^/]+/g, '[^/]+'));
    return regex.test(pathname);
  });
}; 