import { <PERSON>, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/contexts/ThemeContext';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'default' | 'lg' | 'icon';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  showLabel?: boolean;
  dropdown?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className = '', 
  size = 'icon',
  variant = 'ghost',
  showLabel = false,
  dropdown = false
}) => {
  const { theme, actualTheme, setTheme } = useTheme();

  const getIcon = () => {
    switch (actualTheme) {
      case 'light':
        return <Sun className="h-4 w-4 transition-all duration-300 rotate-0 scale-100" />;
      case 'dark':
        return <Moon className="h-4 w-4 transition-all duration-300 rotate-0 scale-100" />;
      default:
        return <Sun className="h-4 w-4 transition-all duration-300 rotate-0 scale-100" />;
    }
  };

  const getTooltipText = () => {
    switch (theme) {
      case 'light':
        return 'Switch to dark theme';
      case 'dark':
        return 'Switch to light theme';
      default:
        return 'Toggle theme';
    }
  };

  const getLabel = () => {
    switch (theme) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      default:
        return 'Theme';
    }
  };

  const toggleTheme = () => {
    // Toggle between light and dark only
    if (theme === 'light') {
      setTheme('dark');
    } else {
      setTheme('light');
    }
  };

  if (dropdown) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className={`transition-all duration-300 hover:scale-105 ${className}`}
            aria-label="Theme options"
          >
            <div className="transition-transform duration-300">
              {getIcon()}
            </div>
            {showLabel && (
              <span className="ml-2 text-sm font-medium">
                {getLabel()}
              </span>
            )}
            <span className="sr-only">Open theme menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[140px]">
          <DropdownMenuItem 
            onClick={() => setTheme('light')}
            className={`flex items-center gap-2 ${theme === 'light' ? 'bg-accent' : ''}`}
          >
            <Sun className="h-4 w-4" />
            <span>Light</span>
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => setTheme('dark')}
            className={`flex items-center gap-2 ${theme === 'dark' ? 'bg-accent' : ''}`}
          >
            <Moon className="h-4 w-4" />
            <span>Dark</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  const buttonContent = (
    <Button
      variant={variant}
      size={size}
      onClick={toggleTheme}
      className={`transition-all duration-300 hover:scale-105 relative overflow-hidden ${className}`}
      aria-label={getTooltipText()}
    >
      <div className="transition-all duration-300 ease-in-out">
        {getIcon()}
      </div>
      {showLabel && (
        <span className="ml-2 text-sm font-medium transition-opacity duration-300">
          {getLabel()}
        </span>
      )}
      <span className="sr-only">{getTooltipText()}</span>
    </Button>
  );

  if (showLabel) {
    return buttonContent;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {buttonContent}
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}; 