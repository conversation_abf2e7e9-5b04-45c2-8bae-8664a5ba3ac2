import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { ChevronUp } from 'lucide-react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const PrivacyPolicy = () => {
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);

  // Scroll to top when component mounts
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Show button when page is scrolled down
  React.useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <h1 className="text-4xl font-bold mb-8 text-center">Privacy Policy</h1>
          
          <div className="bg-white rounded-lg shadow-md p-6 md:p-8 space-y-6">
            <div className="text-gray-600 text-sm mb-6">
              Effective Date: June 11, 2025
            </div>
            
            <section>
              <p className="text-gray-700 mb-4">
                Welcome to Startup Stories. Your privacy is important to us. This policy explains how we collect, use, and protect your information when you use our services.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">1. What Information Do We Collect?</h2>
              <p className="text-gray-700 mb-3">We collect information you provide to us when you:</p>
              <ul className="list-disc pl-6 space-y-1 text-gray-700 mb-3">
                <li>Sign up for an account (name, email address, profile info)</li>
                <li>Use our platform (stories, comments, messages)</li>
                <li>Contact us or respond to communications</li>
              </ul>
              <p className="text-gray-700 mb-3">We may also collect information automatically, such as:</p>
              <ul className="list-disc pl-6 space-y-1 text-gray-700">
                <li>Device and browser details</li>
                <li>IP address and location (general, not precise)</li>
                <li>Usage data (pages visited, features used)</li>
                <li>Cookies (to remember your preferences and improve your experience)</li>
              </ul>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">2. How Do We Use Your Information?</h2>
              <p className="text-gray-700 mb-3">We use your information to:</p>
              <ul className="list-disc pl-6 space-y-1 text-gray-700">
                <li>Create and manage your account</li>
                <li>Provide and improve our services</li>
                <li>Personalize your experience</li>
                <li>Communicate with you about updates, content, and offers</li>
                <li>Analyze usage to make Startup Stories better</li>
              </ul>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">3. Use of Your Stories and Content</h2>
              <p className="text-gray-700 mb-3">When you submit a story or content to Startup Stories, you agree that:</p>
              <ul className="list-disc pl-6 space-y-1 text-gray-700">
                <li>Your story, including your name and profile, may be published and displayed publicly on our platform.</li>
                <li>We may include your submitted story in our premium membership plans or paid sections of the website. This means other users may pay for access to read your story as part of our premium content offering.</li>
                <li>We may use your story to promote Startup Stories on our website, in emails, and across social media.</li>
                <li>We will always attribute your story to your name/profile as you provided.</li>
              </ul>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">4. Will We Share Your Information?</h2>
              <p className="text-gray-700 mb-3">
                We do not sell your personal information. We may share limited data with trusted service providers who help us run our business (such as hosting, analytics, or email delivery).
              </p>
              <p className="text-gray-700 mb-3">
                Some information you choose to share (like your profile, stories, or comments) may be visible to other users or the public, depending on your settings.
              </p>
              <p className="text-gray-700">
                We may share information if required by law or to protect the rights and safety of Startup Stories or our users.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">5. Cookies & Tracking</h2>
              <p className="text-gray-700 mb-3">We use cookies and similar technologies to:</p>
              <ul className="list-disc pl-6 space-y-1 text-gray-700 mb-3">
                <li>Keep you signed in</li>
                <li>Remember your preferences</li>
                <li>Understand how our platform is used</li>
              </ul>
              <p className="text-gray-700">
                You can control cookies in your browser settings, but disabling them may affect your experience.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">6. Your Choices & Rights</h2>
              <p className="text-gray-700 mb-3">You can:</p>
              <ul className="list-disc pl-6 space-y-1 text-gray-700 mb-3">
                <li>Access, update, or delete your profile info from your account settings</li>
                <li>Contact <NAME_EMAIL> to request data deletion or ask questions</li>
              </ul>
              <p className="text-gray-700">
                We may keep some information as required by law or for legitimate business purposes.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">7. Children's Privacy</h2>
              <p className="text-gray-700">
                Startup Stories is not intended for children under 13. We do not knowingly collect personal information from children under 13. If you believe a child has provided us information, contact us and we'll remove it.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">8. Changes to This Policy</h2>
              <p className="text-gray-700">
                We may update this Privacy Policy from time to time. If we make important changes, we'll notify you via our website or email.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">9. Contact Us</h2>
              <p className="text-gray-700">
                Questions about this policy? Email us <NAME_EMAIL>.
              </p>
            </section>
          </div>
        </div>
      </main>
      
      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}
      
      <Footer />
    </div>
  );
};

export default PrivacyPolicy;
