import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Lock, Mail } from 'lucide-react';
import { toast } from "@/components/ui/use-toast";
import { useAuth } from '@/lib/AuthContext';
import api from '@/lib/api';
import AdminThemeWrapper from '@/components/admin/AdminThemeWrapper';
import { useAdminLoginRecaptcha } from '@/hooks/useRecaptcha';
import whitelogo from '../images/startup stories white Logo.png'
import darklogo from '../../images/start stories black Logo.png'
const AdminLogin = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { adminLogin, isAuthenticated, isAdmin } = useAuth();
  const { executeRecaptcha, isReady: recaptchaReady, error: recaptchaError } = useAdminLoginRecaptcha();

  // Check auth status on initial load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Try to access admin test endpoint
        await api.get('/admin/test-auth');
        // If successful, we're already authenticated
        console.log('Already authenticated as admin');
        toast({
          title: "Already authenticated",
          description: "You are already logged in as admin",
        });
        navigate('/admin');
      } catch (error) {
        // If error, we're not authenticated or not admin
        console.log('Not authenticated as admin yet');
      }
    };

    checkAuth();
  }, [navigate]);

  // If already authenticated as admin, redirect to admin dashboard
  useEffect(() => {
    if (isAuthenticated && isAdmin) {
      console.log('Auth context indicates admin is authenticated, redirecting to dashboard');
      navigate('/admin');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Execute reCAPTCHA verification
      let recaptchaToken = null;
      if (recaptchaReady) {
        recaptchaToken = await executeRecaptcha();
        if (recaptchaError) {
          throw new Error('Security verification failed. Please try again.');
        }
      }
      
      console.log('Attempting admin login...');
      // Pass reCAPTCHA token to admin login function
      await adminLogin(email, password, recaptchaToken);
      console.log('Admin login successful');
      toast({
        title: "Login successful",
        description: "Welcome to the admin dashboard",
      });
      navigate('/admin');
    } catch (error: any) {
      console.error('Admin login failed:', error);
      toast({
        title: "Login failed",
        description: error?.message || error?.response?.data?.error || "Invalid admin credentials. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminThemeWrapper>
      <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-4">
            <img 
              src={darklogo} 
              alt="Admin Portal"
              className="h-12 w-auto object-contain"
            />
          </div>
          <CardTitle className="text-2xl font-bold">Admin Login</CardTitle>
          <CardDescription>
            Enter your credentials to access the admin panel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Button variant="link" className="p-0 h-auto text-sm" type="button">
                  Forgot password?
                </Button>
              </div>
              <div className="relative">
                <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  className="pl-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
            </div>
            <Button type="submit" className="w-full" disabled={loading || !recaptchaReady}>
              {loading ? "Logging in..." : "Login to Dashboard"}
            </Button>
            
            {/* reCAPTCHA status indicator */}
            {!recaptchaReady && (
              <p className="text-xs text-gray-500 text-center mt-2">
                Loading security verification...
              </p>
            )}
            {recaptchaError && (
              <p className="text-xs text-red-500 text-center mt-2">
                Security verification unavailable. Please refresh the page.
              </p>
            )}
          </form>
        </CardContent>
        <CardFooter className="border-t pt-4 flex justify-center">
          <p className="text-sm text-gray-600">
            Return to <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>website</Button>
          </p>
        </CardFooter>
      </Card>
    </div>
    </AdminThemeWrapper>
  );
};

export default AdminLogin;
