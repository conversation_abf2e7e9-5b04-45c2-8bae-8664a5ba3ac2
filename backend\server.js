const express = require('express');
const dotenv = require('dotenv');
const connectDB = require('./config/db');
const passport = require('passport');
const corsMiddleware = require('./config/cors');
const cookieParser = require('cookie-parser');

// Load env vars
dotenv.config();

// Connect to database
console.log('Connecting to MongoDB...');
connectDB()
  .then(() => console.log('MongoDB connection successful'))
  .catch(err => console.error('MongoDB connection failed:', err));

// Initialize express
const app = express();

app.use(corsMiddleware);

// Simple logging middleware
app.use((req, res, next) => { 
  next();
});

// Handle Stripe webhooks specifically with raw body
app.use('/api/v1/subscriptions/webhook', express.raw({type: 'application/json'}));

// Standard body parsers for all other routes
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: false, limit: '50mb' }));

// Cookie parser middleware
app.use(cookieParser());

// Make uploads directory static
app.use('/uploads', express.static('uploads'));

// Passport middleware
app.use(passport.initialize());

// Passport config
require('./config/passport')(passport);
 
// Define routes
app.use('/api/auth', require('./routes/auth.routes'));
app.use('/api/users', require('./routes/user.routes'));
app.use('/api/admin', require('./routes/admin.routes'));
app.use('/api/blogs', require('./routes/blog.routes'));
app.use('/api/blog-categories', require('./routes/blogCategory.routes'));
app.use('/api/story-categories', require('./routes/storyCategory.routes'));
app.use('/api/stories', require('./routes/story.routes'));
app.use('/api', require('./routes/comment.routes'));
app.use('/api', require('./routes/blogComment.routes'));
app.use('/api/favorites', require('./routes/favorites.routes'));
app.use('/api/v1/subscriptions', require('./routes/subscription.routes'));
app.use('/api/v1/case-study-subscriptions', require('./routes/caseStudySubscription.routes'));
app.use('/api/startup-profile', require('./routes/startupProfile.routes'));
app.use('/api/case-studies', require('./routes/caseStudy.routes'));
app.use('/api/case-study-comments', require('./routes/caseStudyComment.routes'));

// Test route for connectivity
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working' });
});

// Base route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to Startup Stories API' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(`Error processing ${req.method} ${req.url}:`, err.stack);
  res.status(500).json({
    success: false,
    error: err.message || 'Server Error'
  });
});

// Set port
const PORT = process.env.PORT || 5000;

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:8080'}`);
  console.log(`API base URL: http://localhost:${PORT}/api`);
});