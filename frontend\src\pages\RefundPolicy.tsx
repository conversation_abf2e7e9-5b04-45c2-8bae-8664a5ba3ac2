import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { ChevronUp } from 'lucide-react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const RefundPolicy = () => {
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);

  // Scroll to top when component mounts
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Show button when page is scrolled down
  React.useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <h1 className="text-4xl font-bold mb-8 text-center">Refund Policy</h1>
          
          <div className="bg-white rounded-lg shadow-md p-6 md:p-8 space-y-6">
            <div className="text-gray-600 text-sm mb-6">
              Effective Date: June 11, 2025
            </div>
            
            <section>
              <p className="text-gray-700 mb-4">
                At Startup Stories, we work hard to provide exceptional value to our members through exclusive content, verified profiles, and real founder insights. Please review our refund policy carefully before making any purchase.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">No Refunds on Any Plans</h2>
              <p className="text-gray-700 mb-3">
                All payments made for the Profile Verification Plan ($147/year) and Premium Membership ($197/year) are non-refundable.
              </p>
              <p className="text-gray-700 mb-3">
                By subscribing, you acknowledge and agree that all fees paid are final and cannot be refunded, in whole or in part, regardless of your usage or satisfaction with the service.
              </p>
              <p className="text-gray-700">
                We do not offer pro-rated refunds for partially used subscription periods, accidental purchases, or user-initiated cancellations.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">No Lifetime Deal</h2>
              <p className="text-gray-700 mb-3">
                Startup Stories does not offer lifetime access or one-time-payment lifetime deals for any of our products or memberships.
              </p>
              <p className="text-gray-700">
                All plans are billed annually and renew unless canceled prior to the renewal date.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">Before You Subscribe</h2>
              <p className="text-gray-700 mb-3">
                We encourage you to review our platform features and, if available, take advantage of any free or preview content before subscribing to a paid plan.
              </p>
              <p className="text-gray-700">
                If you have questions about what's included, please reach out to <NAME_EMAIL> before making a purchase.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">Exceptional Circumstances</h2>
              <p className="text-gray-700">
                Refunds may only be granted at the sole discretion of Startup Stories in cases of duplicate payments or other billing errors.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
              <p className="text-gray-700">
                If you believe a billing error has occurred, please contact <NAME_EMAIL> within 7 days of your payment date. We're here to help resolve any genuine mistakes quickly.
              </p>
            </section>
          </div>
        </div>
      </main>
      
      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}
      
      <Footer />
    </div>
  );
};

export default RefundPolicy; 