import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { LayoutGrid, List, ChevronRight, PenTool, Heart, Eye, Calendar, Clock } from 'lucide-react';
import { Blog, Author, BlogCategory } from '@/types';
import homeService from '@/services/homeService';

const BlogsSection = () => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid');
// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

  useEffect(() => {
    fetchBlogs();
  }, []);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const response = await homeService.getLatestBlogs(3);
      setBlogs(response.data);
    } catch (err) {
      setError('Failed to load blogs');
      console.error('Error fetching blogs:', err);
    } finally {
      setLoading(false);
    }
  };

  const getAuthor = (author: string | Author): Author => {
    if (typeof author === 'string') {
      return {
        id: author,
        name: 'Unknown Author',
        username: 'unknown',
        bio: '',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
        createdAt: ''
      };
    }
    return author;
  };

  const getCategoryName = (category: string | BlogCategory): string => {
    if (typeof category === 'string') {
      return 'General';
    }
    return category.name;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getBlogImage = (blog: Blog): string => {
    if (blog.featuredImage?.path) {
      return BACKEND_URL+ blog.featuredImage.path;
    }
    // Default blog images
    const defaultImages = [
      'https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a',
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',
      'https://images.unsplash.com/photo-1551650975-87deedd944c3'
    ];
    return defaultImages[Math.floor(Math.random() * defaultImages.length)];
  };

  if (error) {
    return (
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4 text-foreground">Latest Blogs</h2>
            <p className="text-muted-foreground mb-8">
              Unable to load blogs at the moment. Please try again later.
            </p>
            <Button onClick={fetchBlogs} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 className="text-3xl font-bold mb-2 text-foreground">Latest Blogs</h2>
            <p className="text-muted-foreground max-w-2xl">
              Insights, tutorials, and thought leadership from the entrepreneurship world
            </p>
          </div>
          <div className="flex items-center mt-4 md:mt-0 space-x-2">
            <Button 
              variant={viewType === 'grid' ? 'default' : 'outline'} 
              size="sm" 
              onClick={() => setViewType('grid')}
              className="h-9"
            >
              <LayoutGrid size={16} className="mr-1" /> Grid
            </Button>
            <Button 
              variant={viewType === 'list' ? 'default' : 'outline'} 
              size="sm" 
              onClick={() => setViewType('list')}
              className="h-9"
            >
              <List size={16} className="mr-1" /> List
            </Button>
          </div>
        </div>

        {loading ? (
          <div className={viewType === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-card rounded-xl border border-border p-5 space-y-4">
                <Skeleton className="h-48 w-full rounded-lg" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
                <Skeleton className="h-20 w-full" />
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : blogs.length === 0 ? (
          <div className="text-center py-12">
            <PenTool className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No Blogs Available</h3>
            <p className="text-muted-foreground">
              Check back soon for insightful blogs from industry experts.
            </p>
          </div>
        ) : viewType === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {blogs.map((blog) => {
              const author = getAuthor(blog.author);
              const categoryName = getCategoryName(blog.category);
              
              return (
                <div key={blog._id} className="bg-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-border flex flex-col h-full">
                  <Link to={`/blog/${blog.slug || blog._id}`} target="_blank">
                    <div className="relative">
                      <div className="w-full h-48 overflow-hidden">
                        <img
                          src={getBlogImage(blog)}
                          alt={blog.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {blog.isPremium && (
                        <Badge className="absolute top-3 left-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                          Premium
                        </Badge>
                      )}
                    </div>
                  </Link>
                  <div className="p-5 flex flex-col flex-grow">
                    <div className="flex items-center space-x-2 mb-3">
                      <Badge variant="secondary" className="dark:bg-blue-900/20 dark:text-blue-300">
                        {categoryName}
                      </Badge>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {blog.readTime} min read
                      </div>
                    </div>
                    <Link to={`/blog/${blog.slug || blog._id}`} target="_blank">
                      <h3 className="font-bold text-lg mb-2 line-clamp-2 hover:text-primary transition-colors text-foreground">
                        {blog.title}
                      </h3>
                    </Link>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2 flex-grow">
                      {blog.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center">
                          <Eye className="h-4 w-4 mr-1" />
                          {blog.views}
                        </div>
                        <div className="flex items-center">
                          <Heart className="h-4 w-4 mr-1" />
                          {blog.likes}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(blog.publishDate)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center mt-auto">
                      <Link to={`/author/@${author.username}`} target="_blank" className="flex items-center group">
                        <img 
                          src={getAvatarUrl(author.avatar)} 
                          alt={author.name} 
                          className="w-8 h-8 rounded-full mr-2"
                        />
                        <div>
                          <div className="flex items-center">
                            <p className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                              {author.name}
                            </p>
                            {author.isVerified && (
                              <div className="ml-1">
                                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                                  <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                                </svg>
                              </div>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {author.roleTitle || 'Writer'}
                          </p>
                        </div>
                      </Link>
                      <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80" asChild>
                        <Link to={`/blog/${blog.slug || blog._id}`} target="_blank">
                          Read <ChevronRight size={16} className="ml-1" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="space-y-4">
            {blogs.map((blog) => {
              const author = getAuthor(blog.author);
              const categoryName = getCategoryName(blog.category);
              
              return (
                <div key={blog._id} className="bg-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-border">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/4">
                      <Link to={`/blog/${blog.slug || blog._id}`} target="_blank">
                        <div className="relative">
                          <div className="w-full h-40 md:w-64 md:h-64 overflow-hidden">
                            <img
                              src={getBlogImage(blog)}
                              alt={blog.title}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          {blog.isPremium && (
                            <Badge className="absolute top-3 left-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                              Premium
                            </Badge>
                          )}
                        </div>
                      </Link>
                    </div>
                    <div className="p-5 md:w-3/4 flex flex-col">
                      <div className="flex items-center space-x-2 mb-3">
                        <Badge variant="secondary" className="dark:bg-blue-900/20 dark:text-blue-300">
                          {categoryName}
                        </Badge>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          {blog.readTime} min read
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(blog.publishDate)}
                        </span>
                      </div>
                      <Link to={`/blog/${blog.slug || blog._id}`} target="_blank">
                        <h3 className="font-bold text-xl mb-2 hover:text-primary transition-colors text-foreground">
                          {blog.title}
                        </h3>
                      </Link>
                      <p className="text-muted-foreground mb-4 flex-grow">
                        {blog.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center">
                            <Eye className="h-4 w-4 mr-1" />
                            {blog.views}
                          </div>
                          <div className="flex items-center">
                            <Heart className="h-4 w-4 mr-1" />
                            {blog.likes}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center mt-auto">
                        <Link to={`/author/@${author.username}`} target="_blank" className="flex items-center group">
                          <img 
                            src={getAvatarUrl(author.avatar)} 
                            alt={author.name} 
                            className="w-8 h-8 rounded-full mr-2"
                          />
                          <div>
                            <div className="flex items-center">
                              <p className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                                {author.name}
                              </p>
                              {author.isVerified && (
                                <div className="ml-1">
                                  <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                                    <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                                  </svg>
                                </div>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {author.roleTitle || 'Writer'}
                            </p>
                          </div>
                        </Link>
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/blog/${blog.slug || blog._id}`} target="_blank">
                            Read Blog <ChevronRight size={16} className="ml-1" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        <div className="mt-10 text-center">
          <Button size="lg" style={{ backgroundColor: 'hsl(252.2deg 56.16% 57.06%)', color: 'white' }} className="hover:opacity-90" asChild>
            <Link to="/blogs">View All Blogs</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default BlogsSection; 