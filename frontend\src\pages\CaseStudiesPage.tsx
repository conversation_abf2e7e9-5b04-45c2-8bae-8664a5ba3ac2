import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Layout from '@/components/Layout';
import UserLogin from '@/components/UserLogin';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Eye, Clock, Lock, User, Heart, Grid, List, Bookmark, ChevronUp } from 'lucide-react';
import { CaseStudy, Author } from '@/types';
import caseStudyService, { CaseStudyFilters } from '@/services/caseStudyService';
import favoriteService from '@/services/favoriteService';
import { useAuth } from '@/lib/AuthContext';
import { toast } from 'sonner';

// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

// Helper function to get complete image URL for featured images
const getImageUrl = (path: string) => {
  if (!path) return '/images/default-case-study-cover.jpg';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

const CaseStudiesPage: React.FC = () => {
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [premiumFilter, setPremiumFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [favoritedCaseStudies, setFavoritedCaseStudies] = useState<Set<string>>(new Set());
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const { isAuthenticated, user, loading: authLoading } = useAuth();

  // Helper function to determine if a case study is liked by the current user
  const isLikedByUser = (caseStudy: CaseStudy): boolean => {
    // Don't show as liked if we don't have authenticated user data
    if (!isAuthenticated || !user?.id || !caseStudy.likedBy) {
      return false;
    }
    
    // Check if the current user's ID is in the likedBy array
    return caseStudy.likedBy.some(likedUserId => {
      if (!likedUserId) return false;
      // Convert both to strings for comparison to handle ObjectId/string differences
      return likedUserId.toString() === user.id.toString();
    });
  };

  const fetchCaseStudies = async (page = 1, reset = true) => {
    try {
      setLoading(true);
      const filters: CaseStudyFilters = {
        page: page,
        limit: 12,
        search: searchTerm || undefined,
        status: 'published', // Only show published case studies
        isPremium: premiumFilter !== 'all' ? premiumFilter : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      const response = await caseStudyService.getCaseStudies(filters);
      
      if (reset) {
        setCaseStudies(response.data);
      } else {
        setCaseStudies(prev => [...prev, ...response.data]);
      }
      
      setTotalPages(response.pagination.pages);
      setTotalItems(response.pagination.total);
      setHasMore(page < response.pagination.pages);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching case studies:', error);
      toast.error('Failed to fetch case studies');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchCaseStudies(1, true);
  }, []);

  // Handle filter changes
  useEffect(() => {
    setCurrentPage(1);
    fetchCaseStudies(1, true);
  }, [searchTerm, premiumFilter]);

  // Load more case studies
  const loadMoreCaseStudies = () => {
    if (hasMore && !loading) {
      fetchCaseStudies(currentPage + 1, false);
    }
  };

  // Check favorites status when user is authenticated and case studies are loaded
  useEffect(() => {
    const checkFavoritesStatus = async () => {
      if (isAuthenticated && caseStudies.length > 0) {
        try {
          const favoriteChecks = await Promise.all(
            caseStudies.map(caseStudy => favoriteService.isFavorited('caseStudy', caseStudy._id))
          );
          
          const newFavorites = new Set<string>();
          favoriteChecks.forEach((response, index) => {
            if (response.success && response.data.isFavorited) {
              newFavorites.add(caseStudies[index]._id);
            }
          });
          
          setFavoritedCaseStudies(newFavorites);
        } catch (error) {
          console.error('Error checking favorites status:', error);
        }
      }
    };

    checkFavoritesStatus();
  }, [isAuthenticated, caseStudies]);

  // Show scroll to top button when page is scrolled down
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const toggleLike = async (e: React.MouseEvent, caseStudyId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      const response = await caseStudyService.likeCaseStudy(caseStudyId);

      if (response.success) {
        // Update local state
        setCaseStudies(prev => prev.map(cs => {
          if (cs._id === caseStudyId) {
            return {
              ...cs,
              likes: response.data.likes,
              likedBy: response.data.likedBy || []
            };
          }
          return cs;
        }));

        toast.success(response.data.isLiked ? 'Case study liked!' : 'Like removed');
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Failed to update like status. Please try again.');
    }
  };

  const toggleFavorite = async (e: React.MouseEvent, caseStudyId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      const response = await favoriteService.toggleFavorite('caseStudy', caseStudyId);

      if (response.success) {
        const newFavorites = new Set(favoritedCaseStudies);
        if (response.data.isFavorited) {
          newFavorites.add(caseStudyId);
        } else {
          newFavorites.delete(caseStudyId);
        }
        setFavoritedCaseStudies(newFavorites);

        toast.success(response.data.isFavorited ? 'Case study bookmarked!' : 'Bookmark removed');
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error('Failed to update bookmark status. Please try again.');
    }
  };

  const getAuthorName = (author: string | Author) => {
    if (typeof author === 'string') return 'Unknown Author';
    return author.name || 'Unknown Author';
  };

  const getAuthorAvatar = (author: string | Author) => {
    if (typeof author === 'string') return null;
    return author.avatar;
  };

  const getAuthorUsername = (author: string | Author) => {
    if (typeof author === 'string') return 'unknown';
    return author.username || author.name?.toLowerCase().replace(/\s+/g, '') || 'unknown';
  };

  const openCaseStudy = (caseStudy: CaseStudy) => {
    const url = `/case-study/${caseStudy.slug || caseStudy._id}`;
    window.open(url, '_blank');
  };

  const openAuthorProfile = (e: React.MouseEvent, author: string | Author) => {
    e.preventDefault();
    e.stopPropagation();
    const username = getAuthorUsername(author);
    const url = `/author/@${username}`;
    window.open(url, '_blank');
  };

  // Grid View Component
  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      {caseStudies.length === 0 ? (
        <div className="col-span-full text-center py-12">
          <div className="text-muted-foreground text-lg">No case studies found</div>
          <p className="text-muted-foreground mt-2">Try adjusting your search or filters</p>
        </div>
      ) : (
        caseStudies.map((caseStudy) => {
          const isLiked = isLikedByUser(caseStudy);

          return (
            <Card 
              key={caseStudy._id} 
              className="hover:shadow-lg transition-shadow cursor-pointer border-border bg-card h-full flex flex-col"
              onClick={() => openCaseStudy(caseStudy)}
            >
              <CardHeader className="pb-3 flex-shrink-0">
                <div className="relative w-full h-48">
                  <img
                    src={getImageUrl(caseStudy.featuredImage?.path || '')}
                    alt={caseStudy.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-3 left-3">
                    {caseStudy.isPremium ? (
                      <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700">
                        Premium
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Free
                      </Badge>
                    )}
                  </div>
                  <div className="absolute top-3 right-3 flex space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-8 w-8 bg-background/80 hover:bg-background ${favoritedCaseStudies.has(caseStudy._id) ? 'text-blue-500' : ''}`}
                      onClick={(e) => toggleFavorite(e, caseStudy._id)}
                      title={isAuthenticated ? (favoritedCaseStudies.has(caseStudy._id) ? 'Remove from favorites' : 'Add to favorites') : 'Login to bookmark this case study'}
                    >
                      <Bookmark className={`h-4 w-4 ${favoritedCaseStudies.has(caseStudy._id) ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-8 w-8 bg-background/80 hover:bg-background ${isLiked ? 'text-red-500' : ''}`}
                      onClick={(e) => toggleLike(e, caseStudy._id)}
                      title={isAuthenticated ? (isLiked ? 'Unlike this case study' : 'Like this case study') : 'Login to like this case study'}
                    >
                      <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="h-4 w-4 mr-1" />
                    {caseStudy.readTime} min read
                  </div>
                </div>
                <CardTitle className="text-lg leading-tight mb-2 text-foreground h-12 overflow-hidden line-clamp-2">
                  {caseStudy.title}
                </CardTitle>
                <p className="text-muted-foreground text-sm line-clamp-2 h-10 overflow-hidden">
                  {caseStudy.subheading}
                </p>
              </CardHeader>
              <CardContent className="pt-0 mt-auto">
                <div className="flex items-center justify-between mb-4">
                  <div 
                    className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={(e) => openAuthorProfile(e, caseStudy.author)}
                  >
                    {getAuthorAvatar(caseStudy.author) && (
                      <img
                        src={getAvatarUrl(getAuthorAvatar(caseStudy.author))}
                        alt={getAuthorName(caseStudy.author)}
                        className="w-8 h-8 rounded-full"
                      />
                    )}
                    <div>
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-foreground hover:text-brand-600 transition-colors">
                          {getAuthorName(caseStudy.author)}
                        </div>
                        {typeof caseStudy.author !== 'string' && caseStudy.author.isVerified && (
                          <div className="ml-1">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                              <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                            </svg>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {typeof caseStudy.author !== 'string' ? (caseStudy.author.roleTitle || 'Analyst') : 'Analyst'}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="bg-muted/50 -mx-6 -mb-6 px-6 py-3 text-xs text-muted-foreground flex justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {caseStudy.views}
                    </div>
                    <div className="flex items-center">
                      <Heart className="h-3 w-3 mr-1" />
                      {caseStudy.likes}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })
      )}
    </div>
  );

  // List View Component
  const ListView = () => (
    <div className="space-y-4 mb-8">
      {caseStudies.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-muted-foreground text-lg">No case studies found</div>
          <p className="text-muted-foreground mt-2">Try adjusting your search or filters</p>
        </div>
      ) : (
        caseStudies.map((caseStudy) => {
          const isLiked = isLikedByUser(caseStudy);

          return (
            <div 
              key={caseStudy._id} 
              className="bg-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-border cursor-pointer"
              onClick={() => openCaseStudy(caseStudy)}
            >
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4">
                  <div className="relative">
                    <div className="w-full h-40 md:w-64 md:h-64 overflow-hidden">
                      <img
                        src={getImageUrl(caseStudy.featuredImage?.path || '')}
                        alt={caseStudy.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute top-3 left-3">
                      {caseStudy.isPremium ? (
                        <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700">
                          Premium
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          Free
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="p-5 md:w-3/4 flex flex-col">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 mr-1" />
                      {caseStudy.readTime} min read
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className={`h-8 w-8 ${favoritedCaseStudies.has(caseStudy._id) ? 'text-blue-500' : ''}`}
                        onClick={(e) => toggleFavorite(e, caseStudy._id)}
                        title={isAuthenticated ? (favoritedCaseStudies.has(caseStudy._id) ? 'Remove from favorites' : 'Add to favorites') : 'Login to bookmark this case study'}
                      >
                        <Bookmark className={`h-4 w-4 ${favoritedCaseStudies.has(caseStudy._id) ? 'fill-current' : ''}`} />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        className={`h-8 w-8 ${isLiked ? 'text-red-500' : ''}`}
                        onClick={(e) => toggleLike(e, caseStudy._id)}
                        title={isAuthenticated ? (isLiked ? 'Unlike this case study' : 'Like this case study') : 'Login to like this case study'}
                      >
                        <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                      </Button>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-2 text-foreground hover:text-brand-600 transition-colors">
                    {caseStudy.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 flex-grow">
                    {caseStudy.subheading}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        {caseStudy.views}
                      </div>
                      <div className="flex items-center">
                        <Heart className="h-4 w-4 mr-1" />
                        {caseStudy.likes}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center mt-auto">
                    <div 
                      className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={(e) => openAuthorProfile(e, caseStudy.author)}
                    >
                      {getAuthorAvatar(caseStudy.author) && (
                        <img
                          src={getAvatarUrl(getAuthorAvatar(caseStudy.author))}
                          alt={getAuthorName(caseStudy.author)}
                          className="w-8 h-8 rounded-full"
                        />
                      )}
                      <div>
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-foreground hover:text-brand-600 transition-colors">
                            {getAuthorName(caseStudy.author)}
                          </div>
                          {typeof caseStudy.author !== 'string' && caseStudy.author.isVerified && (
                            <div className="ml-1">
                              <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                                <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                              </svg>
                            </div>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {typeof caseStudy.author !== 'string' ? (caseStudy.author.roleTitle || 'Analyst') : 'Analyst'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        })
      )}
    </div>
  );

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Startup Case Studies
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Learn from successful founders who share their journey, challenges, and strategies
            that helped them build thriving businesses.
          </p>
        </div>

        {/* Filters and View Toggle */}
        <div className="mb-8">
          <Card className="border-border bg-card">
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search case studies..."
                      value={searchTerm}
                      onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setCurrentPage(1);
                      }}
                      className="pl-10 bg-background border-border"
                    />
                  </div>
                </div>
                <Select value={premiumFilter} onValueChange={(value) => {
                  setPremiumFilter(value);
                  setCurrentPage(1);
                }}>
                  <SelectTrigger className="w-full md:w-[180px] bg-background border-border">
                    <SelectValue placeholder="Content Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Content</SelectItem>
                    <SelectItem value="false">Free Content</SelectItem>
                    <SelectItem value="true">Premium Content</SelectItem>
                  </SelectContent>
                </Select>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">View:</span>
                  <Button 
                    variant={viewMode === 'grid' ? 'default' : 'outline'} 
                    size="icon"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid size={18} />
                  </Button>
                  <Button 
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="icon"
                    onClick={() => setViewMode('list')}
                  >
                    <List size={18} />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Case Studies Content */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="text-muted-foreground">Loading case studies...</div>
          </div>
        ) : (
          <>
            {viewMode === 'grid' && <GridView />}
            {viewMode === 'list' && <ListView />}

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center mt-12">
                <Button
                  variant="outline"
                  onClick={loadMoreCaseStudies}
                  disabled={loading}
                  className="px-8"
                >
                  {loading ? 'Loading...' : 'Load More Case Studies'}
                </Button>
              </div>
            )}
          </>
        )}

        {/* Login Dialog */}
        <UserLogin
          isOpen={showLoginDialog}
          onClose={() => setShowLoginDialog(false)}
        />

        {/* Scroll to top button */}
        {scrollToTopVisible && (
          <Button
            variant="outline"
            size="icon"
            className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
            onClick={scrollToTop}
          >
            <ChevronUp size={20} />
            <span className="sr-only">Scroll to top</span>
          </Button>
        )}
      </div>
    </Layout>
  );
};

export default CaseStudiesPage;
