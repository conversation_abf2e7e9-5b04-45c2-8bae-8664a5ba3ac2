import { useEffect } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { HelmetProvider } from 'react-helmet-async';
import { useAuth } from '@/lib/AuthContext';
import { initToastHelper } from '@/lib/toastHelper';
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import StoryDetailPage from "./pages/StoryDetailPage";
import AuthorProfilePage from "./pages/AuthorProfilePage";
import ProfilePage from "./pages/ProfilePage";
import StoryEditorPage from "./pages/StoryEditorPage";
import CategoriesPage from "./pages/CategoriesPage";
import CategoryDetailPage from "./pages/CategoryDetailPage";
import SubscriptionSuccessPage from "./pages/SubscriptionSuccessPage";
import CaseStudySubscriptionSuccessPage from "./pages/CaseStudySubscriptionSuccessPage";
import BlogPage from "./pages/BlogPage";
import StoriesPage from "./pages/StoriesPage";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import RefundPolicy from "./pages/RefundPolicy";
import AcademyPage from "./pages/AcademyPage";
import AdminLogin from "./pages/admin/AdminLogin";
import ProtectedRoute from "./components/ProtectedRoute";

// Admin Pages
import AdminDashboard from "./pages/admin/AdminDashboard";
import StoriesManagement from "./pages/admin/StoriesManagement";
import StoryAdd from "./pages/admin/StoryAdd";
import StoryEdit from "./pages/admin/StoryEdit";
import UserManagement from "./pages/admin/UserManagement";
import UserDetail from "./pages/admin/UserDetail";
import CategoriesManagement from "./pages/admin/CategoriesManagement";

import TransactionHistory from "./pages/admin/TransactionHistory";
import SubscriptionsList from "./pages/admin/SubscriptionsList";
import SubscriptionDetails from "./pages/admin/SubscriptionDetails";
import AdminSettings from "./pages/admin/AdminSettings";
import BlogManagement from "./pages/admin/BlogManagement";
import BlogAdd from "./pages/admin/BlogAdd";
import BlogEdit from "./pages/admin/BlogEdit";
import UserFavorites from "./pages/UserFavorites";
import MyStoriesPage from "./pages/MyStoriesPage";
import BlogPost from "./pages/BlogPost";
import BlogCategories from "./pages/admin/BlogCategories";
import BlogCategoriesManagement from "./pages/admin/BlogCategoriesManagement";
import StartupManagement from "./pages/admin/StartupManagement";
import StartupProfileDetail from "./pages/admin/StartupProfileDetail";
import FounderDirectory from "./pages/FounderDirectory";
import CaseStudiesPage from './pages/CaseStudiesPage';
import CaseStudyDetailPage from './pages/CaseStudyDetailPage';
import CaseStudyManagement from './pages/admin/CaseStudyManagement';
import CaseStudyEditor from './pages/admin/CaseStudyEditor';
import ResetPassword from './pages/ResetPassword';
import Analytics from './components/Analytics';

const queryClient = new QueryClient();
const apiUrl = import.meta.env.VITE_API_URL;
const AppContent = () => {
  const { isAuthenticated, isAdmin, user } = useAuth();

  // Log auth state on app load
  useEffect(() => {
    console.log("API URL: --------------", apiUrl);

    console.log('App loaded with auth state:', {
      isAuthenticated,
      isAdmin,
      userId: user?.id,
      userRole: user?.role
    });

    // Initialize toast helper
    initToastHelper();
  }, [isAuthenticated, isAdmin, user]);

  return (
    <>
      <Analytics />
      <Routes>
      {/* Public routes */}
      <Route path="/" element={<Index />} />
      <Route path="/case-studies" element={<CaseStudiesPage />} />
      <Route path="/case-study/:caseStudyId" element={<CaseStudyDetailPage />} />
      <Route path="/story/:storyId" element={<StoryDetailPage />} />
      <Route path="/author/:authorId" element={<AuthorProfilePage />} />
      <Route path="/author/@:username" element={<AuthorProfilePage />} />
      <Route path="/categories" element={<CategoriesPage />} />
      <Route path="/category/:categoryId" element={<CategoryDetailPage />} />
      <Route path="/blog" element={<BlogPage />} />
      <Route path="/blog/:blogId" element={<BlogPost />} />
      <Route path="/stories" element={<StoriesPage />} />
      <Route path="/privacy-policy" element={<PrivacyPolicy />} />
      <Route path="/terms-of-service" element={<TermsOfService />} />
      <Route path="/refund-policy" element={<RefundPolicy />} />
      <Route path="/academy" element={<AcademyPage />} />
      <Route path="/directory" element={<FounderDirectory />} />

      {/* Password reset route */}
      <Route path="/reset-password/:token" element={<ResetPassword />} />

      {/* User account routes */}
      <Route path="/profile" element={
        <ProtectedRoute>
          <ProfilePage />
        </ProtectedRoute>
      } />
      <Route path="/subscription/success" element={
        <ProtectedRoute>
          <SubscriptionSuccessPage />
        </ProtectedRoute>
      } />
      <Route path="/case-study-subscription/success" element={
        <ProtectedRoute>
          <CaseStudySubscriptionSuccessPage />
        </ProtectedRoute>
      } />
      <Route path="/my-stories" element={
        <ProtectedRoute>
          <MyStoriesPage />
        </ProtectedRoute>
      } />
      <Route path="/favorites" element={
        <ProtectedRoute>
          <UserFavorites />
        </ProtectedRoute>
      } />
      <Route path="/story-editor" element={
        <ProtectedRoute>
          <StoryEditorPage />
        </ProtectedRoute>
      } />
      <Route path="/story-editor/:storyId" element={
        <ProtectedRoute>
          <StoryEditorPage />
        </ProtectedRoute>
      } />

      {/* Admin routes */}
      <Route path="/admin/login" element={<AdminLogin />} />


      <Route path="/admin/case-studies" element={
        <ProtectedRoute requireAdmin>
          <CaseStudyManagement />
        </ProtectedRoute>
      } />

      <Route path="/admin/case-studies/new" element={
        <ProtectedRoute requireAdmin>
          <CaseStudyEditor />
        </ProtectedRoute>
      } />
      
      <Route path="/admin/case-studies/edit/:caseStudyId" element={
        <ProtectedRoute requireAdmin>
          <CaseStudyEditor />
        </ProtectedRoute>
      } />

      <Route path="/admin" element={
        <ProtectedRoute requireAdmin>
          <AdminDashboard />
        </ProtectedRoute>
      } />
      <Route path="/admin/stories" element={
        <ProtectedRoute requireAdmin>
          <StoriesManagement />
        </ProtectedRoute>
      } />
      <Route path="/admin/stories/new" element={
        <ProtectedRoute requireAdmin>
          <StoryAdd />
        </ProtectedRoute>
      } />
      <Route path="/admin/stories/edit/:storyId" element={
        <ProtectedRoute requireAdmin>
          <StoryEdit />
        </ProtectedRoute>
      } />
      <Route path="/admin/users" element={
        <ProtectedRoute requireAdmin>
          <UserManagement />
        </ProtectedRoute>
      } />
      <Route path="/admin/users/:userId" element={
        <ProtectedRoute requireAdmin>
          <UserDetail />
        </ProtectedRoute>
      } />
      <Route path="/admin/categories" element={
        <ProtectedRoute requireAdmin>
          <CategoriesManagement />
        </ProtectedRoute>
      } />
      {/* Redirect old story-categories to categories */}
      <Route path="/admin/story-categories" element={
        <Navigate to="/admin/categories" replace />
      } />
      <Route path="/admin/blog" element={
        <ProtectedRoute requireAdmin>
          <BlogManagement />
        </ProtectedRoute>
      } />
      <Route path="/admin/blog/new" element={
        <ProtectedRoute requireAdmin>
          <BlogAdd />
        </ProtectedRoute>
      } />
      <Route path="/admin/blog/edit/:blogId" element={
        <ProtectedRoute requireAdmin>
          <BlogEdit />
        </ProtectedRoute>
      } />
      <Route path="/admin/blog/categories" element={
        <ProtectedRoute requireAdmin>
          <BlogCategories />
        </ProtectedRoute>
      } />
      <Route path="/admin/management-blog-categories" element={
        <ProtectedRoute requireAdmin>
          <BlogCategoriesManagement />
        </ProtectedRoute>
      } />

      <Route path="/admin/subscriptions" element={
        <ProtectedRoute requireAdmin>
          <SubscriptionsList />
        </ProtectedRoute>
      } />
      <Route path="/admin/subscriptions/:id" element={
        <ProtectedRoute requireAdmin>
          <SubscriptionDetails />
        </ProtectedRoute>
      } />
      <Route path="/admin/transactions" element={
        <ProtectedRoute requireAdmin>
          <TransactionHistory />
        </ProtectedRoute>
      } />
      <Route path="/admin/settings" element={
        <ProtectedRoute requireAdmin>
          <AdminSettings />
        </ProtectedRoute>
      } />

      {/* Startup Management Routes */}
      <Route path="/admin/startup-profiles" element={
        <ProtectedRoute requireAdmin>
          <StartupManagement />
        </ProtectedRoute>
      } />
      <Route path="/admin/startup-profiles/:id" element={
        <ProtectedRoute requireAdmin>
          <StartupProfileDetail />
        </ProtectedRoute>
      } />

      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
      <Route path="*" element={<NotFound />} />
    </Routes>
    </>
  );
};

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AppContent />
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </HelmetProvider>
);

export default App;