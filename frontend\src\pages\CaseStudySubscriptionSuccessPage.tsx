import React, { useEffect, useState } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, BookOpen, ArrowRight, Sparkles } from 'lucide-react';
import caseStudySubscriptionService from '@/services/caseStudySubscriptionService';

const CaseStudySubscriptionSuccessPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [isVerifying, setIsVerifying] = useState(true);
  const [subscription, setSubscription] = useState<any>(null);
  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    const verifySubscription = async () => {
      try {
        // Give Stripe webhooks a moment to process
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if the subscription is active
        const currentSubscription = await caseStudySubscriptionService.getCurrentSubscription();
        setSubscription(currentSubscription);
      } catch (error) {
        console.error('Error verifying subscription:', error);
      } finally {
        setIsVerifying(false);
      }
    };

    if (sessionId) {
      verifySubscription();
    } else {
      setIsVerifying(false);
    }
  }, [sessionId]);

  const benefits = [
    {
      title: "Access All Premium Content",
      description: "Unlock every premium section in all case studies - past, present, and future."
    },
    {
      title: "Growth Strategies Revealed",
      description: "See the exact tactics, channels, and frameworks that helped founders scale."
    },
    {
      title: "Revenue & Metrics",
      description: "Get detailed financial breakdowns, conversion rates, and growth metrics."
    },
    {
      title: "Founder Mistakes & Lessons",
      description: "Learn from costly mistakes and pivot decisions to avoid repeating them."
    }
  ];

  if (isVerifying) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto py-16">
          <div className="max-w-2xl mx-auto text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h1 className="text-2xl font-bold mb-2">Processing your subscription...</h1>
            <p className="text-gray-600">Please wait while we set up your premium access.</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto py-16">
        <div className="max-w-2xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome to Premium Case Studies! 🎉
            </h1>
            <p className="text-xl text-gray-600">
              You now have access to all premium content across every case study.
            </p>
          </div>

          {/* Subscription Details */}
          {subscription && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Sparkles className="h-5 w-5 mr-2 text-yellow-500" />
                  Your Subscription
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Plan</p>
                    <p className="font-medium">Annual</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <p className="font-medium text-green-600 capitalize">{subscription.status}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Next Billing</p>
                    <p className="font-medium">
                      {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Access Level</p>
                    <p className="font-medium text-blue-600">Premium</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* What You Get */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="h-5 w-5 mr-2 text-blue-600" />
                What You Get Access To
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium text-gray-900">{benefit.title}</h3>
                      <p className="text-sm text-gray-600">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Next Steps</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div>
                  <h3 className="font-medium text-blue-900">Explore Case Studies</h3>
                  <p className="text-sm text-blue-700">Start reading premium content right away</p>
                </div>
                <Button asChild>
                  <Link to="/case-studies">
                    Browse Studies
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h3 className="font-medium text-gray-900">Manage Subscription</h3>
                  <p className="text-sm text-gray-700">Update billing, download receipts, or cancel</p>
                </div>
                <Button 
                  variant="outline"
                  onClick={() => caseStudySubscriptionService.redirectToPortal()}
                >
                  Manage Billing
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Support */}
          <div className="text-center text-sm text-gray-600">
            <p>Questions about your subscription?</p>
            <p>
              Contact us at{' '}
              <a href="<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CaseStudySubscriptionSuccessPage; 