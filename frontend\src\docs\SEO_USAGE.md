# Dynamic SEO Implementation Guide

This guide explains how to use the dynamic SEO system implemented in the Startup Stories React application.

## Overview

The SEO system uses `react-helmet-async` to dynamically manage meta tags, Open Graph tags, Twitter cards, and structured data based on route and content.

## Components

### 1. SEO Component (`src/components/SEO.tsx`)

The main SEO component that renders meta tags:

```tsx
import SEO from "@/components/SEO";

// Basic usage
<SEO 
  title="Page Title"
  description="Page description"
  keywords="keyword1, keyword2"
  image="/path/to/image.jpg"
  type="website"
/>
```

### 2. SEO Utilities (`src/utils/seoUtils.ts`)

Helper functions to generate SEO data for different content types:

- `generateStorySEO(story)` - For story pages
- `generateBlogSEO(blog)` - For blog posts
- `generateAuthorSE<PERSON>(user)` - For author profiles
- `generateCategorySEO(category)` - For category pages
- `generatePageSEO(pageType)` - For static pages

### 3. useSEO Hook (`src/hooks/useSEO.ts`)

Custom hook for easy SEO management:

```tsx
import { useSEO } from "@/hooks/useSEO";

const { generateSEO, getPageSEO } = useSEO();
```

## Usage Examples

### Static Pages

For static pages like homepage, about, etc.:

```tsx
import SEO from "@/components/SEO";
import { useSEO } from "@/hooks/useSEO";

const HomePage = () => {
  const { generateSEO } = useSEO();
  const homeSEO = generateSEO.page('home');

  return (
    <div>
      <SEO {...homeSEO} />
      {/* Page content */}
    </div>
  );
};
```

### Dynamic Content Pages

For pages with dynamic content like stories or blog posts:

```tsx
import SEO from "@/components/SEO";
import { useSEO } from "@/hooks/useSEO";
import { generateStoryStructuredData } from "@/utils/seoUtils";
import { Helmet } from 'react-helmet-async';

const StoryPage = () => {
  const [story, setStory] = useState(null);
  const { generateSEO } = useSEO();

  // Generate SEO when story is loaded
  const storySEO = story ? generateSEO.story({
    _id: story._id,
    title: story.title,
    excerpt: story.excerpt,
    content: story.content,
    featuredImage: story.featuredImage,
    author: {
      name: story.author.name,
      username: story.author.username
    },
    category: {
      name: story.category.name
    },
    tags: story.tags,
    createdAt: story.createdAt,
    updatedAt: story.updatedAt
  }) : null;

  // Generate structured data
  const structuredData = story ? generateStoryStructuredData(story) : null;

  return (
    <div>
      {/* SEO Meta Tags */}
      {storySEO && <SEO {...storySEO} />}
      
      {/* Structured Data */}
      {structuredData && (
        <Helmet>
          <script type="application/ld+json">
            {JSON.stringify(structuredData)}
          </script>
        </Helmet>
      )}

      {/* Page content */}
    </div>
  );
};
```

### Custom SEO

For custom SEO configuration:

```tsx
import SEO from "@/components/SEO";

const CustomPage = () => {
  const customSEO = {
    title: "Custom Page Title | Startup Stories",
    description: "Custom page description for better SEO",
    keywords: "custom, keywords, seo",
    image: "/custom-image.jpg",
    type: "website" as const,
    canonical: "https://example.com/custom-page"
  };

  return (
    <div>
      <SEO {...customSEO} />
      {/* Page content */}
    </div>
  );
};
```

## SEO Props Interface

```tsx
interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  canonical?: string;
}
```

## Generated Meta Tags

The SEO component automatically generates:

### Basic Meta Tags
- `<title>`
- `<meta name="description">`
- `<meta name="keywords">`
- `<meta name="author">`
- `<link rel="canonical">`

### Open Graph Tags
- `og:title`
- `og:description`
- `og:type`
- `og:url`
- `og:image`
- `og:site_name`
- Article-specific tags (for articles)

### Twitter Card Tags
- `twitter:card`
- `twitter:site`
- `twitter:title`
- `twitter:description`
- `twitter:image`

### Structured Data
- Organization schema
- Article schema (for stories/blogs)

## Best Practices

1. **Always include SEO component** in page components
2. **Use descriptive titles** (50-60 characters)
3. **Write compelling descriptions** (150-160 characters)
4. **Include relevant keywords** naturally
5. **Use high-quality images** for social sharing
6. **Set canonical URLs** to avoid duplicate content
7. **Use structured data** for better search understanding

## Configuration

### Environment Variables

Set these in your `.env` file:

```env
VITE_FRONTEND_URL=https://your-domain.com
VITE_API_URL=https://api.your-domain.com
```

### Route Configuration

Update `src/config/seoConfig.ts` to add new static routes:

```tsx
{
  path: '/new-page',
  seo: {
    title: 'New Page | Startup Stories',
    description: 'Description for the new page',
    keywords: 'relevant, keywords',
    type: 'website'
  }
}
```

## Testing SEO

1. **View source** to check meta tags
2. **Use browser dev tools** to inspect head elements
3. **Test with social media debuggers**:
   - Facebook Sharing Debugger
   - Twitter Card Validator
   - LinkedIn Post Inspector
4. **Use SEO tools**:
   - Google Search Console
   - Screaming Frog
   - SEMrush

## Troubleshooting

### Meta tags not updating
- Ensure `HelmetProvider` wraps your app
- Check that SEO component is rendered
- Verify data is loaded before generating SEO

### Social sharing not working
- Check image URLs are absolute
- Verify Open Graph tags are present
- Test with social media debuggers

### Search engines not indexing
- Check for `noIndex` flag
- Verify canonical URLs
- Submit sitemap to search engines 