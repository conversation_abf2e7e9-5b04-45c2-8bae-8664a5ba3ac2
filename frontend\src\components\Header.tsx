import { useEffect, useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronUp, Menu, User, X } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import UserLogin from './UserLogin';
import UserRegistration from './UserRegistration';
import { useAuth } from '@/lib/AuthContext';
import { ThemeToggle } from './ThemeToggle';
import { useTheme } from '@/contexts/ThemeContext';
import whitelogo from '../images/startup stories white Logo.png'
import darklogo from '../images/start stories black Logo.png'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showRegistrationPopup, setShowRegistrationPopup] = useState(false);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const { user, isAuthenticated, isAdmin, logout } = useAuth();
  const { actualTheme } = useTheme();
  const navigate = useNavigate();

  // Show button when page is scrolled down
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  // Get the appropriate logo based on theme
  const getLogoSrc = () => {
    return actualTheme === 'dark' 
      ? whitelogo
      : darklogo;
  };

  return (
    <div>
      <header className="sticky top-0 w-full bg-background/90 backdrop-blur-sm border-b border-border z-50">
        <div className="container mx-auto flex items-center justify-between h-16 px-4 sm:px-6">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 flex-shrink-0">
            <img 
              src={getLogoSrc()} 
              alt="Startup Stories"
              className="h-8 sm:h-10 w-auto object-contain"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-4 xl:space-x-6">
            <Link to="/" className="text-sm font-medium text-foreground hover:text-primary transition-colors">Home</Link>
            <Link to="/stories" className="text-sm font-medium text-foreground hover:text-primary transition-colors">Stories</Link>
            <Link to="/case-studies" className="text-sm font-medium text-foreground hover:text-primary transition-colors">Case Studies</Link>
            <Link to="/blog" className="text-sm font-medium text-foreground hover:text-primary transition-colors">Blog</Link>
            <Link to="/categories" className="text-sm font-medium text-foreground hover:text-primary transition-colors">Categories</Link>
            <Link to="/directory" className="text-sm font-medium text-foreground hover:text-primary transition-colors hidden xl:block">Directory</Link>
            
            {/* Theme Toggle */}
            <ThemeToggle dropdown={true} />
            
            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-9 w-9 rounded-full" aria-label="User menu">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={getAvatarUrl(user?.avatar)} />
                      <AvatarFallback>{user?.name ? getInitials(user.name) : 'U'}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-0.5 leading-none">
                      <p className="font-medium text-sm">{user?.name}</p>
                      <p className="text-xs text-muted-foreground w-[150px] truncate">{user?.email}</p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/profile">Profile</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/my-stories">My Stories</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/favorites">My Favorites</Link>
                  </DropdownMenuItem>
                  {isAdmin && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin">Admin Dashboard</Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button onClick={() => setShowLoginPopup(true)} variant="outline" size="sm">Log In</Button>
                <Button onClick={() => setShowRegistrationPopup(true)} size="sm">Register</Button>
              </div>
            )}
          </nav>

          {/* Mobile menu button and theme toggle */}
          <div className="lg:hidden flex items-center space-x-2">
            <ThemeToggle />
            <button
              className="p-2 touch-target-sm rounded-md hover:bg-muted transition-colors"
              onClick={toggleMenu}
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-16 left-0 right-0 bg-background border-b border-border shadow-lg z-40">
            <div className="container mx-auto py-4 px-4 space-y-2">
              <Link
                to="/"
                className="block py-3 px-2 text-base font-medium text-foreground hover:text-primary hover:bg-muted rounded-md transition-colors touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                to="/stories"
                className="block py-3 px-2 text-base font-medium text-foreground hover:text-primary hover:bg-muted rounded-md transition-colors touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Success Stories
              </Link>
              <Link
                to="/case-studies"
                className="block py-3 px-2 text-base font-medium text-foreground hover:text-primary hover:bg-muted rounded-md transition-colors touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Case Studies
              </Link>
              <Link
                to="/blog"
                className="block py-3 px-2 text-base font-medium text-foreground hover:text-primary hover:bg-muted rounded-md transition-colors touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Blog
              </Link>
              <Link
                to="/categories"
                className="block py-3 px-2 text-base font-medium text-foreground hover:text-primary hover:bg-muted rounded-md transition-colors touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Categories
              </Link>
              <Link
                to="/directory"
                className="block py-3 px-2 text-base font-medium text-foreground hover:text-primary hover:bg-muted rounded-md transition-colors touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Founder Directory
              </Link>
                            
              {isAuthenticated ? (
                <div className="pt-2 border-t">
                  <div className="flex items-center space-x-3 mb-4">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={user?.avatar} alt={user?.name} />
                      <AvatarFallback>{user?.name ? getInitials(user.name) : 'U'}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-sm">{user?.name}</p>
                      <p className="text-xs text-muted-foreground">{user?.email}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Link to="/profile" className="block py-2 text-sm font-medium text-foreground hover:text-primary transition-colors">
                      Profile
                    </Link>
                    <Link to="/my-stories" className="block py-2 text-sm font-medium text-foreground hover:text-primary transition-colors">
                      My Stories
                    </Link>
                    <Link to="/favorites" className="block py-2 text-sm font-medium text-foreground hover:text-primary transition-colors">
                      My Favorites
                    </Link>
                    {isAdmin && (
                      <Link to="/admin" className="block py-2 text-sm font-medium text-foreground hover:text-primary transition-colors">
                        Admin Dashboard
                      </Link>
                    )}
                    <Button onClick={handleLogout} variant="outline" className="w-full mt-2">
                      Log out
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col space-y-2 pt-2">
                  <Button onClick={() => {
                    setIsMenuOpen(false);
                    setShowLoginPopup(true);
                  }} variant="outline" className="w-full justify-center">
                    Log In
                  </Button>
                  <Button onClick={() => {
                    setIsMenuOpen(false);
                    setShowRegistrationPopup(true);
                  }} className="w-full justify-center">
                    Register
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}
      </header>

      {showRegistrationPopup && (
        <UserRegistration
          isOpen={showRegistrationPopup}
          onClose={() => setShowRegistrationPopup(false)}
        />
      )}

      {showLoginPopup && (
        <UserLogin
          isOpen={showLoginPopup}
          onClose={() => setShowLoginPopup(false)}
        />
      )}

      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}
    </div>
  );
};

export default Header;
