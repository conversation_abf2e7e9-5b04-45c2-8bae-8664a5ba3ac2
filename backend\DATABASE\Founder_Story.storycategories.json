[{"_id": {"$oid": "6808be0ed846921d71d20006"}, "name": "Adventure", "slug": "adventure", "description": "Epic journeys filled with danger, discovery, and heroic quests.", "storiesCount": 0, "accessLevel": "Premium", "createdAt": {"$date": "2025-04-23T10:16:46.129Z"}, "__v": 0}, {"_id": {"$oid": "6808be1fd846921d71d2000b"}, "name": "Science Fiction", "slug": "science-fiction", "description": "Futuristic narratives featuring technology, space, time travel, and AI.", "storiesCount": 0, "accessLevel": "Free", "createdAt": {"$date": "2025-04-23T10:17:03.891Z"}, "__v": 0}, {"_id": {"$oid": "6808be38d846921d71d20010"}, "name": "Slice of Life", "slug": "slice-of-life", "description": "Simple, everyday experiences that reflect real-life emotions and moments.", "storiesCount": 0, "accessLevel": "Premium", "createdAt": {"$date": "2025-04-23T10:17:28.353Z"}, "__v": 0}, {"_id": {"$oid": "6808be4fd846921d71d20015"}, "name": "Fantasy", "slug": "fantasy", "description": "Magical worlds, mythical creatures, and legendary adventures beyond reality.", "storiesCount": 0, "accessLevel": "Free", "createdAt": {"$date": "2025-04-23T10:17:51.737Z"}, "__v": 0}, {"_id": {"$oid": "6808be60d846921d71d2001a"}, "name": "Thriller", "slug": "thriller", "description": "High-stakes plots with suspense, twists, and edge-of-your-seat tension.", "storiesCount": 0, "accessLevel": "Free", "createdAt": {"$date": "2025-04-23T10:18:08.697Z"}, "__v": 0}, {"_id": {"$oid": "68282a62e4a6867262586b14"}, "name": "Crypto", "slug": "crypto", "description": "Crypto", "storiesCount": 1, "accessLevel": "Free", "createdAt": {"$date": "2025-05-17T06:19:14.862Z"}, "__v": 0}]