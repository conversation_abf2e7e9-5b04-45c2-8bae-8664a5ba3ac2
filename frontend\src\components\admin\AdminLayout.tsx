import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  BarChart3, Users, FileText, Tags, CreditCard, Settings, LogOut, Menu, X,
  Home, Bell, Search, PenSquare, Briefcase, Receipt, CircleDollarSign,
  BookOpen
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAuth } from '@/lib/AuthContext';
import { toast } from '@/components/ui/use-toast';
import AdminThemeWrapper from './AdminThemeWrapper';
import whitelogo from '../../images/startup stories white Logo.png'
import darklogo from '../../images/start stories black Logo.png'
interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();

  // Force light theme for admin section
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove dark class and force light theme
    root.classList.remove('dark');
    
    // Store original theme state
    const originalTheme = localStorage.getItem('theme');
    
    // Set light theme temporarily
    localStorage.setItem('theme', 'light');
    
    // Cleanup: restore original theme when component unmounts
    return () => {
      if (originalTheme) {
        localStorage.setItem('theme', originalTheme);
        // Only restore dark theme if the original theme was dark
        if (originalTheme === 'dark') {
          root.classList.add('dark');
        }
      }
    };
  }, []);

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: BarChart3, current: location.pathname === '/admin' },
    { name: 'Users', href: '/admin/users', icon: Users, current: location.pathname.includes('/admin/users') },
    { name: 'Startup Profiles', href: '/admin/startup-profiles', icon: Briefcase, current: location.pathname.includes('/admin/startup-profiles') },
    { name: 'Case Studies', href: '/admin/case-studies', icon: BookOpen, current: location.pathname.includes('/admin/case-studies') },
    { name: 'Blog Posts', href: '/admin/blog', icon: PenSquare, current: location.pathname.includes('/admin/blog') },
    { name: 'Blog Categories', href: '/admin/management-blog-categories', icon: PenSquare, current: location.pathname.includes('/admin/management-blog-categories') },
    { name: 'Stories', href: '/admin/stories', icon: FileText, current: location.pathname.includes('/admin/stories') },
    { name: 'Story Categories', href: '/admin/categories', icon: Tags, current: location.pathname === '/admin/categories' || location.pathname.includes('/admin/story-categories') },

    { name: 'Subscriptions', href: '/admin/subscriptions', icon: CircleDollarSign, current: location.pathname.includes('/admin/subscriptions') && !location.pathname.includes('/admin/subscriptions/') },
    { name: 'Transactions', href: '/admin/transactions', icon: Receipt, current: location.pathname === '/admin/transactions' },
    { name: 'Settings', href: '/admin/settings', icon: Settings, current: location.pathname.includes('/admin/settings') },
  ];

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logout successful",
        description: "You have been logged out successfully"
      });
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: "Logout failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Get initials for avatar
  const getInitials = (name: string | undefined) => {
    if (!name) return 'AD';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  return (
    <AdminThemeWrapper>
      <div className="h-full min-h-screen bg-gray-50">
        {/* Mobile sidebar */}
        <div className={`fixed inset-0 z-50 bg-gray-900/80 ${sidebarOpen ? 'block' : 'hidden'} lg:hidden`} onClick={() => setSidebarOpen(false)}></div>

        <div className={`fixed inset-y-0 left-0 z-50 w-72 bg-white ${sidebarOpen ? 'block' : 'hidden'} lg:block`}>
          <div className="flex h-full flex-col">
            {/* Sidebar header */}
            <div className="flex h-16 items-center justify-between border-b px-6">
              <div className="flex items-center">
                <img 
                  src={darklogo} 
                  alt="Admin Portal"
                  className="h-8 w-auto object-contain"
                />
                
              </div>
              <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(false)} className="lg:hidden">
                <X size={20} />
              </Button>
            </div>

            {/* Sidebar content */}
            <ScrollArea className="flex-1 px-4 py-4">
              <nav className="space-y-1">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`
                      flex items-center px-4 py-2 text-sm font-medium rounded-md 
                      ${item.current ? 'bg-brand-50 text-brand-700' : 'text-gray-700 hover:bg-gray-100'}
                    `}
                  >
                    <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                    {item.name}
                  </Link>
                ))}
              </nav>
            </ScrollArea>

            {/* Sidebar footer */}
            <div className="border-t p-4">
              <div className="flex items-center">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.avatar} alt={user?.name} />
                  <AvatarFallback>{getInitials(user?.name)}</AvatarFallback>
                </Avatar>
                <div className="ml-3">
                  <p className="text-sm font-medium">{user?.name || 'Admin User'}</p>
                  <p className="text-xs text-gray-500">{user?.email || '<EMAIL>'}</p>
                </div>
              </div>
              <Button variant="ghost" className="mt-3 w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50" onClick={handleLogout}>
                <LogOut className="mr-3 h-5 w-5" />
                Sign out
              </Button>
            </div>
          </div>
        </div>

      {/* Main content */}
      <div className="lg:pl-72">
        {/* Top navbar */}
        <div className="sticky top-0 z-10 flex h-16 items-center border-b bg-white px-4 lg:px-6">
          {/* Mobile menu button */}
          <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(true)} className="lg:hidden">
            <Menu size={20} />
          </Button>

          <div className="flex flex-1 items-center justify-between pl-4 lg:pl-0">
            {/* Search */}
            <div className="max-w-lg flex-1">
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  placeholder="Search..."
                  className="pl-10"
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Link to="/" className="hidden md:block text-sm text-gray-700 hover:text-brand-600">
                <Home className="inline-block mr-1 h-4 w-4" />
                Back to Site
              </Link>
               
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="p-6">
          {children}
        </div>
      </div>
      </div>
    </AdminThemeWrapper>
  );
};

export default AdminLayout;
