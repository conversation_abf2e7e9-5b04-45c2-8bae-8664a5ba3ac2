# Google OAuth Configuration for Startup Stories

This document provides instructions for setting up Google OAuth in the Startup Stories application.

## Prerequisites

1. A Google Cloud Platform account (https://console.cloud.google.com/)
2. Access to the application's frontend and backend code

## Setup Steps

### 1. Create OAuth Credentials in Google Cloud Platform

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" and select "OAuth client ID"
5. Set the application type to "Web application"
6. Add a name for your client (e.g., "Startup Stories")
7. Add authorized JavaScript origins:
   - For development: `http://localhost:8080`
   - For production: Add your production domain
8. Add authorized redirect URIs:
   - For development: `http://localhost:5000/api/auth/google/callback`
   - For production: Add your production callback URL
9. Click "Create" to generate your credentials

### 2. Configure Backend Environment Variables

1. Open your backend `.env` file
2. Update the following variables with your Google credentials:
   ```
   GOOGLE_CLIENT_ID=your-client-id-from-google-cloud
   GOOGLE_CLIENT_SECRET=your-client-secret-from-google-cloud
   GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback
   ```

### 3. Configure Frontend Google Client ID

1. Open `frontend/src/lib/googleAuth.ts`
2. Update the `GOOGLE_CLIENT_ID` constant with your Google client ID:
   ```typescript
   const GOOGLE_CLIENT_ID = 'your-client-id-from-google-cloud';
   ```

## Testing the Integration

1. Start your backend server: `cd backend && npm run dev`
2. Start your frontend server: `cd frontend && npm run dev`
3. Open the application in your browser
4. Click "Login" and test the "Continue with Google" button
5. You should be redirected to Google's authentication page
6. After successful authentication, you should be redirected back to your application and logged in

## Troubleshooting

If you encounter issues with Google OAuth:

1. Check that your Google Cloud Console has the Google Identity Services API enabled
2. Verify that your authorized origins and redirect URIs match your application's URLs
3. Make sure your client ID is correctly set in both frontend and backend
4. Check the browser console for any error messages
5. Verify that your backend Google OAuth routes are correctly configured

For more detailed information, see [Google's OAuth 2.0 documentation](https://developers.google.com/identity/protocols/oauth2). 