const Story = require('../models/Story.model');
const StoryCategory = require('../models/StoryCategory.model');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { sendStorySubmissionConfirmationEmail, sendStoryPublishedNotificationEmail } = require('../utils/emailService');

/**
 * @desc    Get all published stories
 * @route   GET /api/stories
 * @access  Public
 */
exports.getStories = async (req, res) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 12;
    const startIndex = (page - 1) * limit;

    // Build filter object
    const filter = { status: 'published' };
    
    // Add isPremium filter if specified
    if (req.query.isPremium !== undefined) {
      filter.isPremium = req.query.isPremium === 'true';
    }
    
    // Add category filter if specified
    if (req.query.category && req.query.category !== 'all') {
      filter.category = req.query.category;
    }
    
    // Add author filter if specified
    if (req.query.author && req.query.author !== 'all') {
      filter.author = req.query.author;
    }
    
    // Add search filter if specified
    if (req.query.search) {
      filter.$or = [
        { title: { $regex: req.query.search, $options: 'i' } },
        { excerpt: { $regex: req.query.search, $options: 'i' } },
        { content: { $regex: req.query.search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    const sort = {};
    sort[sortBy] = sortOrder;

    // Query with pagination - explicitly select likedBy field
    const stories = await Story.find(filter)
      .populate('author', 'name username avatar role email isVerified roleTitle')
      .populate('category', 'name')
      .select('+likedBy') // Explicitly include likedBy field
      .sort(sort)
      .skip(startIndex)
      .limit(limit);

    // Get total count
    const total = await Story.countDocuments(filter);

    res.status(200).json({
      success: true,
      count: stories.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: stories
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Get stories by user
 * @route   GET /api/stories/user/mystories
 * @access  Private
 */
exports.getMyStories = async (req, res) => {
  try {
    const stories = await Story.find({ author: req.user.id })
      .populate('category', 'name')
      .sort({ updatedAt: -1 });

    res.status(200).json({
      success: true,
      count: stories.length,
      data: stories
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Get single story by ID or slug
 * @route   GET /api/stories/:id
 * @access  Public
 */
exports.getStoryById = async (req, res) => {
  try {
    let query = {};

    // Check if id is valid MongoDB ObjectId
    if (mongoose.isValidObjectId(req.params.id)) {
      query._id = req.params.id;
    } else {
      // If not, treat as slug
      query.slug = req.params.id;
    }

    const story = await Story.findOne(query)
      .populate('author', 'name username avatar roleTitle email twitterUrl websiteUrl linkedinUrl location bio isVerified')
      .populate('category', 'name')
      .select('+likedBy'); // Explicitly include likedBy field

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Increment view count
    story.views = story.views + 1;
    await story.save();

    res.status(200).json({
      success: true,
      data: story
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Create new story
 * @route   POST /api/stories
 * @access  Private
 */
exports.createStory = async (req, res) => {
  try {
    // Add author to request body
    req.body.author = req.user.id;

    // Validate category exists
    if (!req.body.category) {
      return res.status(400).json({
        success: false,
        error: "Category is required"
      });
    }

    // Validate category is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(req.body.category)) {
      return res.status(400).json({
        success: false,
        error: "Invalid category ID format"
      });
    }

    // Check if category exists in database
    const categoryExists = await StoryCategory.findById(req.body.category);
    if (!categoryExists) {
      return res.status(400).json({
        success: false,
        error: "Selected category doesn't exist"
      });
    }

    // Parse tags if they're a comma-separated string
    if (typeof req.body.tags === 'string') {
      req.body.tags = req.body.tags.split(',').map(tag => tag.trim());
    }

    // Create story
    const story = await Story.create(req.body);

    // Populate the created story and include likedBy field
    const populatedStory = await Story.findById(story._id)
      .populate('author', 'name username avatar role email isVerified')
      .populate('category', 'name')
      .select('+likedBy');

    res.status(201).json({
      success: true,
      data: populatedStory
    });
  } catch (err) {
    if (err.name === 'ValidationError') {
      const messages = Object.values(err.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Server Error: ' + err.message
      });
    }
  }
};

/**
 * @desc    Update story
 * @route   PUT /api/stories/:id
 * @access  Private
 */
exports.updateStory = async (req, res) => {
  try {
    let story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Check if request is from admin interface
    const isAdminRequest = req.isFromAdmin === true;

    console.log(`Update story - Admin request: ${isAdminRequest}, User role: ${req.user.role}`);

    // Make sure user is story owner or admin
    if (story.author.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update this story'
      });
    }

    // If it's an admin request and user is not admin, reject
    if (isAdminRequest && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin privileges required for this operation'
      });
    }

    // Validate category if it's being updated
    if (req.body.category) {
      // Validate category is a valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(req.body.category)) {
        return res.status(400).json({
          success: false,
          error: "Invalid category ID format"
        });
      }

      // Check if category exists in database
      const categoryExists = await StoryCategory.findById(req.body.category);
      if (!categoryExists) {
        return res.status(400).json({
          success: false,
          error: "Selected category doesn't exist"
        });
      }
    }

    // Parse tags if they're a comma-separated string
    if (typeof req.body.tags === 'string') {
      req.body.tags = req.body.tags.split(',').map(tag => tag.trim());
    }

    // Update story using findOneAndUpdate
    // This will use the pre middleware for handling slug conflicts
    story = await Story.findOneAndUpdate(
      { _id: req.params.id },
      { $set: req.body },
      { new: true, runValidators: true }
    ).populate('category', 'name')
     .select('+likedBy'); // Explicitly include likedBy field

    res.status(200).json({
      success: true,
      data: story
    });
  } catch (err) {
    if (err.name === 'ValidationError') {
      const messages = Object.values(err.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else if (err.code === 11000) {
      // Handle duplicate key error
      return res.status(400).json({
        success: false,
        error: 'A story with this title already exists. Please choose a different title.'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Server Error: ' + err.message
      });
    }
  }
};

/**
 * @desc    Delete story
 * @route   DELETE /api/stories/:id
 * @access  Private
 */
exports.deleteStory = async (req, res) => {
  try {
    const story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    const isAdminRequest = req.isFromAdmin === true;

    console.log(`Delete story - Admin request: ${isAdminRequest}, User role: ${req.user.role}`);

    // Make sure user is story owner or admin
    if (story.author.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to delete this story'
      });
    }

    // If it's an admin request and user is not admin, reject
    if (isAdminRequest && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin privileges required for this operation'
      });
    }

    // Delete image if it's not the default
    if (story.featuredImage && !story.featuredImage.includes('default-story.jpg')) {
      const imagePath = path.join(__dirname, '..', story.featuredImage);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }

    // Store category before deletion
    const categoryId = story.category;

    await story.deleteOne();

    // Decrement storiesCount for the category
    await StoryCategory.findByIdAndUpdate(categoryId, {
      $inc: { storiesCount: -1 }
    });

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Upload featured image for story
 * @route   POST /api/stories/upload
 * @access  Private
 */
exports.uploadStoryImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Please upload a file'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        filePath: req.file.path,
        fileName: req.file.filename
      }
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Like a story
 * @route   PUT /api/stories/:id/like
 * @access  Private
 */
exports.likeStory = async (req, res) => {
  try {
    const story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Check if story already liked by user
    if (story.likedBy.includes(req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Story already liked'
      });
    }

    // Add user to likedBy array and increment likes
    story.likedBy.push(req.user.id);
    story.likes = story.likedBy.length;

    await story.save();

    res.status(200).json({
      success: true,
      data: {
        likes: story.likes,
        likedBy: story.likedBy,
        isLiked: true
      }
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Unlike a story
 * @route   PUT /api/stories/:id/unlike
 * @access  Private
 */
exports.unlikeStory = async (req, res) => {
  try {
    const story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Check if story not liked by user
    if (!story.likedBy.includes(req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Story not liked yet'
      });
    }

    // Remove user from likedBy array and decrement likes
    story.likedBy = story.likedBy.filter(
      id => id.toString() !== req.user.id
    );
    story.likes = story.likedBy.length;

    await story.save();

    res.status(200).json({
      success: true,
      data: {
        likes: story.likes,
        likedBy: story.likedBy,
        isLiked: false
      }
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Toggle like/unlike a story
 * @route   PUT /api/stories/:id/toggle-like
 * @access  Private
 */
exports.toggleStoryLike = async (req, res) => {
  try {
    const story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    const isLiked = story.likedBy.includes(req.user.id);

    if (isLiked) {
      // Unlike the story
      story.likedBy = story.likedBy.filter(
        id => id.toString() !== req.user.id
      );
      story.likes = story.likedBy.length;
    } else {
      // Like the story
      story.likedBy.push(req.user.id);
      story.likes = story.likedBy.length;
    }

    await story.save();

    res.status(200).json({
      success: true,
      data: {
        likes: story.likes,
        likedBy: story.likedBy,
        isLiked: !isLiked
      }
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Submit story for publishing (change status to pending)
 * @route   PUT /api/stories/:id/publish
 * @access  Private
 */
exports.publishStory = async (req, res) => {
  try {
    let story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Make sure user is story owner
    if (story.author.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to publish this story'
      });
    }

    // Update story status to pending using findOneAndUpdate
    // This will use the pre middleware for handling slug conflicts
    story = await Story.findOneAndUpdate(
      { _id: req.params.id },
      { $set: { status: 'pending' } },
      { new: true, runValidators: true }
    ).populate('category', 'name').populate('author', 'name email');

    // Send story submission confirmation email (don't wait for it to complete)
    try {
      await sendStorySubmissionConfirmationEmail(story.author.email, story.author.name, story.title);
    } catch (emailError) {
      console.error('Story submission confirmation email failed to send:', emailError);
      // Don't fail the story submission if email fails
    }

    res.status(200).json({
      success: true,
      data: story
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Save story as draft
 * @route   PUT /api/stories/:id/draft
 * @access  Private
 */
exports.saveDraft = async (req, res) => {
  try {
    let story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Make sure user is story owner
    if (story.author.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to save this story'
      });
    }

    // Update story status to draft using findOneAndUpdate
    // This will use the pre middleware for handling slug conflicts
    story = await Story.findOneAndUpdate(
      { _id: req.params.id },
      { $set: { status: 'draft' } },
      { new: true, runValidators: true }
    ).populate('category', 'name');

    res.status(200).json({
      success: true,
      data: story
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Get stories by category
 * @route   GET /api/stories/category/:categoryId
 * @access  Public
 */
exports.getStoriesByCategory = async (req, res) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 12;
    const startIndex = (page - 1) * limit;

    // Build filter object
    const filter = {
      category: req.params.categoryId,
      status: 'published'
    };

    // Add search filter if specified
    if (req.query.search) {
      filter.$or = [
        { title: { $regex: req.query.search, $options: 'i' } },
        { excerpt: { $regex: req.query.search, $options: 'i' } },
        { content: { $regex: req.query.search, $options: 'i' } }
      ];
    }

    // Query with pagination
    const stories = await Story.find(filter)
      .populate('author', 'name username avatar role email')
      .populate('category', 'name')
      .select('+likedBy') // Explicitly include likedBy field
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Get total count
    const total = await Story.countDocuments(filter);

    res.status(200).json({
      success: true,
      count: stories.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: stories
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Get stories by tag
 * @route   GET /api/stories/tag/:tag
 * @access  Public
 */
exports.getStoriesByTag = async (req, res) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Query with pagination
    const stories = await Story.find({
      tags: req.params.tag,
      status: 'published'
    })
      .populate('author', 'name username avatar role email')
      .populate('category', 'name')
      .select('+likedBy') // Explicitly include likedBy field
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Get total count
    const total = await Story.countDocuments({
      tags: req.params.tag,
      status: 'published'
    });

    res.status(200).json({
      success: true,
      count: stories.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: stories
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Get all stories (for admin)
 * @route   GET /api/stories/admin/all
 * @access  Private (Admin only)
 */
exports.getAllStoriesAdmin = async (req, res) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Filters
    const status = req.query.status || 'all';
    const category = req.query.category || 'all';
    const filter = {};

    if (status !== 'all') {
      filter.status = status;
    }

    if (category !== 'all' && mongoose.isValidObjectId(category)) {
      filter.category = category;
    }

    // Query with pagination
    const stories = await Story.find(filter)
      .populate('author', 'name username avatar role email isVerified')
      .populate('category', 'name')
      .select('+likedBy') // Explicitly include likedBy field
      .sort({ updatedAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Get total count
    const total = await Story.countDocuments(filter);

    res.status(200).json({
      success: true,
      count: stories.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: stories
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Approve story (change status to published)
 * @route   PUT /api/stories/:id/approve
 * @access  Private (Admin only)
 */
exports.approveStory = async (req, res) => {
  try {
    let story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Update story status to published
    story = await Story.findOneAndUpdate(
      { _id: req.params.id },
      {
        $set: {
          status: 'published',
          adminFeedback: '' // Clear any previous feedback
        }
      },
      { new: true, runValidators: true }
    ).populate('category', 'name').populate('author', 'name username email');

    // Increment storiesCount for the category
    await StoryCategory.findByIdAndUpdate(story.category, {
      $inc: { storiesCount: 1 }
    });

    // Send story published notification email (don't wait for it to complete)
    try {
      await sendStoryPublishedNotificationEmail(story.author.email, story.author.name, story.title, story.slug);
    } catch (emailError) {
      console.error('Story published notification email failed to send:', emailError);
      // Don't fail the story approval if email fails
    }

    res.status(200).json({
      success: true,
      data: story
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
};

/**
 * @desc    Reject story (change status to rejected) with feedback
 * @route   PUT /api/stories/:id/reject
 * @access  Private (Admin only)
 */
exports.rejectStory = async (req, res) => {
  try {
    if (!req.body.feedback) {
      return res.status(400).json({
        success: false,
        error: 'Please provide feedback for rejection'
      });
    }

    let story = await Story.findById(req.params.id);

    if (!story) {
      return res.status(404).json({
        success: false,
        error: 'Story not found'
      });
    }

    // Update story status to rejected with feedback
    story = await Story.findOneAndUpdate(
      { _id: req.params.id },
      {
        $set: {
          status: 'rejected',
          adminFeedback: req.body.feedback
        }
      },
      { new: true, runValidators: true }
    ).populate('category', 'name').populate('author', 'name username email');

    res.status(200).json({
      success: true,
      data: story
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
}; 

/**
 * @desc    Get random stories by author (excluding current story), fallback to other authors if insufficient
 * @route   GET /api/stories/author/:authorId/random
 * @access  Public
 */
exports.getRandomStoriesByAuthor = async (req, res) => {
  try {
    const { authorId } = req.params;
    const { excludeStoryId } = req.query;
    const limit = parseInt(req.query.limit, 10) || 3;

    // Validate authorId is a valid ObjectId
    if (!mongoose.isValidObjectId(authorId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid author ID format'
      });
    }

    // Build filter for same author - convert authorId string to ObjectId
    const sameAuthorFilter = {
      author: new mongoose.Types.ObjectId(authorId),
      status: 'published'
    };

    // Exclude current story if provided
    if (excludeStoryId && mongoose.isValidObjectId(excludeStoryId)) {
      sameAuthorFilter._id = { $ne: new mongoose.Types.ObjectId(excludeStoryId) };
    }

    // Get random stories from same author using MongoDB aggregation
    const sameAuthorStories = await Story.aggregate([
      { $match: sameAuthorFilter },
      { $sample: { size: limit } },
      {
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'author',
          pipeline: [
            {
              $project: {
                name: 1,
                username: 1,
                avatar: 1,
                role: 1,
                email: 1,
                isVerified: 1,
                roleTitle: 1
              }
            }
          ]
        }
      },
      {
        $lookup: {
          from: 'storycategories',
          localField: 'category',
          foreignField: '_id',
          as: 'category',
          pipeline: [
            {
              $project: {
                name: 1
              }
            }
          ]
        }
      },
      {
        $unwind: {
          path: '$author',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $unwind: {
          path: '$category',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          title: 1,
          excerpt: 1,
          featuredImage: 1,
          slug: 1,
          views: 1,
          likes: 1,
          createdAt: 1,
          author: 1,
          category: 1,
          tags: 1
        }
      }
    ]);

    let stories = sameAuthorStories;

    // If we don't have enough stories from the same author, get more from other authors
    if (stories.length < limit) {
      const remainingCount = limit - stories.length;
      
      // Filter for different authors, excluding current story
      const otherAuthorsFilter = {
        author: { $ne: new mongoose.Types.ObjectId(authorId) },
        status: 'published'
      };

      if (excludeStoryId && mongoose.isValidObjectId(excludeStoryId)) {
        otherAuthorsFilter._id = { $ne: new mongoose.Types.ObjectId(excludeStoryId) };
      }

      // Get random stories from other authors
      const otherAuthorStories = await Story.aggregate([
        { $match: otherAuthorsFilter },
        { $sample: { size: remainingCount } },
        {
          $lookup: {
            from: 'users',
            localField: 'author',
            foreignField: '_id',
            as: 'author',
            pipeline: [
              {
                $project: {
                  name: 1,
                  username: 1,
                  avatar: 1,
                  role: 1,
                  email: 1,
                  isVerified: 1,
                  roleTitle: 1
                }
              }
            ]
          }
        },
        {
          $lookup: {
            from: 'storycategories',
            localField: 'category',
            foreignField: '_id',
            as: 'category',
            pipeline: [
              {
                $project: {
                  name: 1
                }
              }
            ]
          }
        },
        {
          $unwind: {
            path: '$author',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $unwind: {
            path: '$category',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $project: {
            title: 1,
            excerpt: 1,
            featuredImage: 1,
            slug: 1,
            views: 1,
            likes: 1,
            createdAt: 1,
            author: 1,
            category: 1,
            tags: 1
          }
        }
      ]);

      // Combine stories (same author first, then others)
      stories = [...stories, ...otherAuthorStories];
    }

    res.status(200).json({
      success: true,
      count: stories.length,
      data: stories
    });
  } catch (err) {
    console.error('Error in getRandomStoriesByAuthor:', err);
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + err.message
    });
  }
}; 