import { Helmet } from 'react-helmet-async';
import { useEffect } from 'react';

const Analytics: React.FC = () => {
  useEffect(() => {
    console.log('Analytics scripts loaded');
  }, []);

  return (
    <Helmet>
      {/* Microsoft Clarity */}
      <script type="text/javascript">
        {`
          (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "rxsmvf395g");
        `}
      </script>

      {/* Google Analytics (gtag.js) */}
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-5E1G97L1TX"></script>
      <script>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-5E1G97L1TX');
        `}
      </script>
    </Helmet>
  );
};

export default Analytics; 