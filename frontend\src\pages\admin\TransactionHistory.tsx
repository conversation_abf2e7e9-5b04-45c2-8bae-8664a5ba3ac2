import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ChevronUp, Search, Filter, ArrowUp, ArrowDown, Download, Calendar, Loader2 } from 'lucide-react';
import { Card } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from 'date-fns';
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import AdminLayout from '@/components/admin/AdminLayout';
import UserDisplayCard from '@/components/admin/UserDisplayCard';
import axios from 'axios';
import { useToast } from '@/components/ui/use-toast';
import { Link } from 'react-router-dom';

// Define transaction interface
interface Transaction {
  id: string;
  invoiceId: string;
  amountPaid: number;
  currency: string;
  paidAt: string;
  paymentStatus: string;
  receiptUrl?: string;
  subscriptionId: string;
  subscriptionPlan: string;
  subscriptionType?: string;
  userId: string;
  userName: string;
  userEmail: string;
  userUsername?: string;
  userAvatar?: string;
  userIsVerified?: boolean;
}

// Define stats interface
interface TransactionStats {
  completed: number;
  failed: number;
  refunded: number;
  totalRevenue: number;
}

const TransactionHistory = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const [sortField, setSortField] = useState<'paidAt' | 'amountPaid'>('paidAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<TransactionStats>({
    completed: 0,
    failed: 0,
    refunded: 0,
    totalRevenue: 0
  });

  
  
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const { toast } = useToast();

  // Helper function to format date as dd/mm/yy
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString().slice(-2);
    return `${day}/${month}/${year}`;
  };

  // Navigate to author profile
  const viewAuthorProfile = (username: string) => {
    window.open(`http://localhost:8080/author/@${username}`, '_blank');
  };

  // Helper function to get avatar URL
  const getAvatarUrl = (path: string) => {
    if (!path) return '/default-avatar.png';
    if (path.startsWith('http')) return path;
    if (path.startsWith('/')) return `http://localhost:5000${path}`;
    return `http://localhost:5000/${path}`;
  };

  // Fetch transactions from API
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/admin/transactions', {
          params: {
            status: statusFilter !== 'all' ? statusFilter : undefined,
            search: searchTerm || undefined,
            dateFrom: dateFilter ? format(dateFilter, 'yyyy-MM-dd') : undefined
          }
        });
        
        setTransactions(response.data.data);
        setStats(response.data.stats);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching transactions:', error);
        toast({
          title: 'Error',
          description: 'Failed to load transaction data. Please try again.',
          variant: 'destructive'
        });
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [statusFilter, searchTerm, dateFilter, toast]);

  // Show button when page is scrolled down
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Apply client-side sorting
  const sortedTransactions = [...transactions].sort((a, b) => {
    if (sortField === 'paidAt') {
      const dateA = new Date(a.paidAt).getTime();
      const dateB = new Date(b.paidAt).getTime();
      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
    }
    
    if (sortField === 'amountPaid') {
      return sortDirection === 'asc' ? a.amountPaid - b.amountPaid : b.amountPaid - a.amountPaid;
    }
    
    return 0;
  });
  
  // Handle sort toggle
  const handleSort = (field: 'paidAt' | 'amountPaid') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Get transaction status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'refunded':
        return <Badge className="bg-amber-100 text-amber-800">Refunded</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case 'unknown':
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status || 'Unknown'}</Badge>;
    }
  };

  // Get subscription type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'verification':
        return <Badge className="bg-blue-100 text-blue-800">Verification</Badge>;
      case 'case-study':
        return <Badge className="bg-purple-100 text-purple-800">Case Study</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{type || 'Unknown'}</Badge>;
    }
  };
  
  // Export transactions as CSV
  const exportTransactionsCSV = () => {
    // Create CSV headers
    const headers = ['Transaction ID', 'User', 'Email', 'Type', 'Plan', 'Amount', 'Date', 'Status'];
    
    // Create CSV rows
    const rows = transactions.map(tx => [
      tx.invoiceId,
      tx.userName,
      tx.userEmail,
      tx.subscriptionType || 'Unknown',
      tx.subscriptionPlan,
      `${tx.amountPaid} ${tx.currency}`,
      format(new Date(tx.paidAt), 'dd/MM/yyyy HH:mm'),
      tx.paymentStatus
    ]);
    
    // Combine headers and rows
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `transactions-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Get unique plans for filter
  const uniquePlans = Array.from(new Set(transactions.map(tx => tx.subscriptionPlan)));
  
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold">Transaction History</h1>
            <p className="text-gray-500">View and manage payment transactions</p>
          </div>
          <Button variant="outline" className="mt-4 md:mt-0" onClick={exportTransactionsCSV}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
          <Card className="p-4 bg-green-50 border-green-200">
            <p className="text-sm text-green-600">Completed Transactions</p>
            <p className="text-2xl font-bold">{stats.completed || 0}</p>
          </Card>
          
          <Card className="p-4 bg-red-50 border-red-200">
            <p className="text-sm text-red-600">Failed</p>
            <p className="text-2xl font-bold">{stats.failed || 0}</p>
          </Card>
          
          <Card className="p-4 bg-blue-50 border-blue-200">
            <p className="text-sm text-blue-600">Total Revenue</p>
            <p className="text-2xl font-bold">
              ${(stats.totalRevenue || 0).toFixed(2)}
            </p>
          </Card>
        </div>
        
        {/* Filters */}
        <div className="bg-white p-4 rounded-lg border shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">Search</label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  id="search"
                  placeholder="Search by ID, user, or email..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="paid">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label htmlFor="plan" className="block text-sm font-medium text-gray-700 mb-1">Plan</label>
              <Select value={planFilter} onValueChange={setPlanFilter}>
                <SelectTrigger id="plan">
                  <SelectValue placeholder="Filter by plan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Plans</SelectItem>
                  {uniquePlans.map(plan => (
                    <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <Calendar className="mr-2 h-4 w-4" />
                    {dateFilter ? format(dateFilter, 'dd/MM/yyyy') : 'Select a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={dateFilter}
                    onSelect={setDateFilter}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="flex items-end">
              <Button variant="outline" className="w-full" onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setPlanFilter('all');
                setDateFilter(undefined);
                setSortField('paidAt');
                setSortDirection('desc');
              }}>
                <Filter className="mr-2 h-4 w-4" />
                Reset Filters
              </Button>
            </div>
          </div>
        </div>
        
        {/* Transactions Table */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            {loading ? (
              <div className="flex justify-center items-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading transactions...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice ID</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('amountPaid')}>
                      Amount
                      {sortField === 'amountPaid' && (
                        sortDirection === 'asc' ? <ArrowUp className="inline ml-1 h-4 w-4" /> : <ArrowDown className="inline ml-1 h-4 w-4" />
                      )}
                    </TableHead>
                    <TableHead>Invoice</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('paidAt')}>
                      Date
                      {sortField === 'paidAt' && (
                        sortDirection === 'asc' ? <ArrowUp className="inline ml-1 h-4 w-4" /> : <ArrowDown className="inline ml-1 h-4 w-4" />
                      )}
                    </TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTransactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-10">
                        <p className="text-gray-500">No transactions found matching your criteria</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedTransactions.map((tx) => (
                      <TableRow key={tx.id}>
                        <TableCell className="font-mono text-sm">{tx.invoiceId || 'N/A'}</TableCell>
                        <TableCell>
                          {tx.userName ? (
                            <UserDisplayCard 
                              user={{
                                _id: tx.userId,
                                name: tx.userName,
                                email: tx.userEmail,
                                username: tx.userUsername || '',
                                avatar: tx.userAvatar,
                                isVerified: tx.userIsVerified
                              }}
                              size="sm"
                            />
                          ) : (
                            <span className="text-gray-500">Unknown User</span>
                          )}
                        </TableCell>
                        <TableCell>{getTypeBadge(tx.subscriptionType || 'unknown')}</TableCell>
                        <TableCell>{tx.subscriptionPlan || 'N/A'}</TableCell>
                        <TableCell>${(tx.amountPaid || 0).toFixed(2)}</TableCell>
                        <TableCell>
                          {tx.receiptUrl ? (
                            <a 
                              href={tx.receiptUrl} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-primary hover:underline"
                            >
                              <Download  size={22} className="ml-2" />
                            </a>
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                        <TableCell>{tx.paidAt ? `${formatDate(tx.paidAt)} ${format(new Date(tx.paidAt), 'HH:mm')}` : 'N/A'}</TableCell>
                        <TableCell>{getStatusBadge(tx.paymentStatus || 'unknown')}</TableCell>
                        <TableCell>
                          {tx.subscriptionId ? (
                            <Link 
                              to={`/admin/subscriptions/${tx.subscriptionId}`}
                              className="text-primary hover:underline text-sm"
                            >
                              View Subscription
                            </Link>
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </div>
        </div>
      </div>
      
      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}
    </AdminLayout>
  );
};

export default TransactionHistory;
