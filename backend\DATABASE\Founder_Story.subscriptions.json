[{"_id": {"$oid": "683951a5bb7a1ceb821efa76"}, "subscriptionId": "sub_1RUMXALZNpJ3WQd54McNrrTx", "__v": 1, "cancelAtPeriodEnd": false, "createdAt": {"$date": "2025-05-30T06:35:17.314Z"}, "currentPeriodEnd": {"$date": "2026-05-30T06:35:18.727Z"}, "currentPeriodStart": {"$date": "2025-05-30T06:35:17.989Z"}, "paymentHistory": [{"invoiceId": "in_1RUMXALZNpJ3WQd5ws8wWiGn", "amountPaid": 300, "currency": "usd", "paidAt": {"$date": "2025-05-30T06:35:13.000Z"}, "paymentStatus": "paid", "receiptUrl": "https://invoice.stripe.com/i/acct_19OmI6LZNpJ3WQd5/test_YWNjdF8xOU9tSTZMWk5wSjNXUWQ1LF9TUEFzOG1mZVE4VzZudmlQTzZZcVJJcjc3SE1wclFlLDEzOTEyNzcxOA02002Fit43ok?s=ap", "_id": {"$oid": "683951a63725d405e5e868cc"}, "createdAt": {"$date": "2025-05-30T06:35:18.732Z"}, "updatedAt": {"$date": "2025-05-30T06:35:18.732Z"}}], "plan": "yearly", "status": "active", "stripeCustomerId": "cus_SHf5hzGkE70RuQ", "subscriptionType": "case-study", "updatedAt": {"$date": "2025-05-30T06:35:18.733Z"}, "userId": {"$oid": "682ad9136ca3666a54bea9fa"}}, {"_id": {"$oid": "683951cdbb7a1ceb821efab4"}, "subscriptionId": "sub_1RUMXoLZNpJ3WQd5bXP9XWtm", "__v": 1, "cancelAtPeriodEnd": false, "createdAt": {"$date": "2025-05-30T06:35:57.441Z"}, "currentPeriodEnd": {"$date": "2026-05-30T06:36:00.661Z"}, "currentPeriodStart": {"$date": "2025-05-30T06:36:00.661Z"}, "paymentHistory": [{"invoiceId": "in_1RUMXoLZNpJ3WQd5yE9weND7", "amountPaid": 20, "currency": "usd", "paidAt": {"$date": "2025-05-30T06:35:54.000Z"}, "paymentStatus": "paid", "receiptUrl": "https://invoice.stripe.com/i/acct_19OmI6LZNpJ3WQd5/test_YWNjdF8xOU9tSTZMWk5wSjNXUWQ1LF9TUEF0Q1hSTVVPSEVWMkl3SUdJUm9aWWtxTVlTZ3QwLDEzOTEyNzc1OA0200dUP7maxX?s=ap", "_id": {"$oid": "683951ce3725d405e5e868fc"}, "createdAt": {"$date": "2025-05-30T06:35:58.210Z"}, "updatedAt": {"$date": "2025-05-30T06:35:58.210Z"}}], "plan": "monthly", "status": "active", "stripeCustomerId": "cus_SHf5hzGkE70RuQ", "subscriptionType": "verification", "updatedAt": {"$date": "2025-05-30T06:36:00.662Z"}, "userId": {"$oid": "682ad9136ca3666a54bea9fa"}}, {"_id": {"$oid": "68395556bb7a1ceb821efed5"}, "subscriptionId": "sub_1RUMmPLZNpJ3WQd5oWV5HhqS", "__v": 1, "cancelAtPeriodEnd": false, "createdAt": {"$date": "2025-05-30T06:51:02.522Z"}, "currentPeriodEnd": {"$date": "2026-05-30T06:51:03.256Z"}, "currentPeriodStart": {"$date": "2025-05-30T06:51:02.837Z"}, "paymentHistory": [{"invoiceId": "in_1RUMmPLZNpJ3WQd5dJkBgfzy", "amountPaid": 20, "currency": "usd", "paidAt": {"$date": "2025-05-30T06:50:59.000Z"}, "paymentStatus": "paid", "receiptUrl": "https://invoice.stripe.com/i/acct_19OmI6LZNpJ3WQd5/test_YWNjdF8xOU9tSTZMWk5wSjNXUWQ1LF9TUEI4TDE0Q1VCcWZxVFprTmIzVU9WR09OT3pENXl4LDEzOTEyODY2Mw02001FLTYjSt?s=ap", "_id": {"$oid": "6839555745ddd4e3adb35064"}, "createdAt": {"$date": "2025-05-30T06:51:03.267Z"}, "updatedAt": {"$date": "2025-05-30T06:51:03.267Z"}}], "plan": "monthly", "status": "active", "stripeCustomerId": "cus_SMDgPGuc1y3sR1", "subscriptionType": "verification", "updatedAt": {"$date": "2025-05-30T06:51:03.268Z"}, "userId": {"$oid": "682eeb5d30ac52ab48e0270e"}}, {"_id": {"$oid": "6839559bbb7a1ceb821eff44"}, "subscriptionId": "sub_1RUMnWLZNpJ3WQd532jzRP7B", "__v": 1, "cancelAtPeriodEnd": false, "createdAt": {"$date": "2025-05-30T06:52:11.123Z"}, "currentPeriodEnd": {"$date": "2026-05-30T06:52:11.872Z"}, "currentPeriodStart": {"$date": "2025-05-30T06:52:11.589Z"}, "paymentHistory": [{"invoiceId": "in_1RUMnWLZNpJ3WQd5PLfv93gl", "amountPaid": 300, "currency": "usd", "paidAt": {"$date": "2025-05-30T06:52:08.000Z"}, "paymentStatus": "paid", "receiptUrl": "https://invoice.stripe.com/i/acct_19OmI6LZNpJ3WQd5/test_YWNjdF8xOU9tSTZMWk5wSjNXUWQ1LF9TUEI5OHJOVDFkTmVyNGk2SkdJQ0VtNnd5NUMycFFxLDEzOTEyODczMQ0200s7Wxe969?s=ap", "_id": {"$oid": "6839559b45ddd4e3adb350ff"}, "createdAt": {"$date": "2025-05-30T06:52:11.875Z"}, "updatedAt": {"$date": "2025-05-30T06:52:11.875Z"}}], "plan": "yearly", "status": "active", "stripeCustomerId": "cus_SMDgPGuc1y3sR1", "subscriptionType": "case-study", "updatedAt": {"$date": "2025-05-30T06:52:11.875Z"}, "userId": {"$oid": "682eeb5d30ac52ab48e0270e"}}, {"_id": {"$oid": "6843b72d4069f10c2a58e693"}, "subscriptionId": "sub_1RXDmjLZNpJ3WQd5pbebDplo", "__v": 1, "cancelAtPeriodEnd": false, "createdAt": {"$date": "2025-06-07T03:51:09.823Z"}, "currentPeriodEnd": {"$date": "2026-06-07T03:51:10.749Z"}, "currentPeriodStart": {"$date": "2025-06-07T03:51:10.171Z"}, "paymentHistory": [{"invoiceId": "in_1RXDmjLZNpJ3WQd5lLjFJT7U", "amountPaid": 20000, "currency": "usd", "paidAt": {"$date": "2025-06-07T03:51:07.000Z"}, "paymentStatus": "paid", "receiptUrl": "https://invoice.stripe.com/i/acct_19OmI6LZNpJ3WQd5/test_YWNjdF8xOU9tSTZMWk5wSjNXUWQ1LF9TUzgyRVpUMkd0T1hLQ2tNcURiNDFwaFlSZkMzMGJGLDEzOTgwOTA3MQ0200G5mme1yq?s=ap", "_id": {"$oid": "6843b72e60531e86892d30c9"}, "createdAt": {"$date": "2025-06-07T03:51:10.755Z"}, "updatedAt": {"$date": "2025-06-07T03:51:10.755Z"}}], "plan": "yearly", "status": "active", "stripeCustomerId": "cus_SS82ZJqtoF3QoT", "subscriptionType": "verification", "updatedAt": {"$date": "2025-06-07T03:51:10.755Z"}, "userId": {"$oid": "6843b67d60531e86892d3086"}}]