import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Image as ImageIcon, Save, Search, X, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { getBlogCategories, getBlog, createBlog, updateBlog, searchUsers } from '@/services/blogService';
import WysiwygEditor from "@/components/WysiwygEditor";

interface User {
  _id: string;
  id?: string; // Optional id field to handle different object structures
  name: string;
  email: string;
}

interface CreateBlogData {
  title: string;
  excerpt: string;
  content: string;
  author: string;
  category: string;
  tags?: string;
  status?: 'draft' | 'published';
  publishDate?: string;
  isPremium?: boolean;
  featuredImage?: File;
}

interface UpdateBlogData extends Partial<CreateBlogData> { }

const BlogAdd = () => {
  const navigate = useNavigate();
  const { blogId: id } = useParams();
  const { toast } = useToast();
  const isEdit = Boolean(id);

  // Form state
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState('');
  const [featuredImage, setFeaturedImage] = useState<File | null>(null);
  const [featuredImagePreview, setFeaturedImagePreview] = useState<string | null>(null);
  const [publishDate, setPublishDate] = useState<Date | undefined>(new Date());
  const [status, setStatus] = useState<'draft' | 'published'>('draft');

  // Author selection state
  const [selectedAuthor, setSelectedAuthor] = useState<User | null>(null);
  const [authorSearchQuery, setAuthorSearchQuery] = useState('');
  const [authorSearchResults, setAuthorSearchResults] = useState<User[]>([]);
  const [isSearchingAuthors, setIsSearchingAuthors] = useState(false);
  const [showAuthorDropdown, setShowAuthorDropdown] = useState(false);

  // Other state
  const [categories, setCategories] = useState<Array<{ _id: string; name: string; slug: string }>>([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEdit);

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await getBlogCategories();
      console.log('Categories response:', response);
      console.log('Categories data:', response.data);

      // Handle paginated response
      const categoriesData = response.data || [];
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: "Error",
        description: "Failed to fetch categories",
        variant: "destructive",
      });
    }
  };

  // Fetch blog data for editing
  const fetchBlogData = async () => {
    if (!id) return;

    try {
      setInitialLoading(true);
      const response = await getBlog(id);
      const blog = response.data;

      setTitle(blog.title);
      setContent(blog.content);
      setExcerpt(blog.excerpt);
      setCategory(typeof blog.category === 'object' ? blog.category._id : blog.category);
      setTags(blog.tags.join(', '));
      setStatus(blog.status);
      setPublishDate(new Date(blog.publishDate));

      // Handle author - blog.author should be a populated object with _id, name, etc.
      if (blog.author) {
        console.log('Blog author from backend:', blog.author);
        // Ensure the author object has the correct structure
        const authorObj = {
          _id: blog.author._id || blog.author.id,
          id: blog.author.id || blog.author._id,
          name: blog.author.name,
          email: blog.author.email
        };
        setSelectedAuthor(authorObj);
        setAuthorSearchQuery(blog.author.name || blog.author.email || '');
      }

      if (blog.featuredImage?.url) {
        const imageUrl = blog.featuredImage.url.startsWith('/uploads')
          ? `http://localhost:5000${blog.featuredImage.url}`
          : blog.featuredImage.url;
        setFeaturedImagePreview(imageUrl);
      }

    } catch (error) {
      console.error('Error fetching blog:', error);
      toast({
        title: "Error",
        description: "Failed to fetch blog data",
        variant: "destructive",
      });
      navigate('/admin/blog');
    } finally {
      setInitialLoading(false);
    }
  };

  // Search authors
  const searchAuthors = async (query: string) => {
    if (query.length < 2) {
      setAuthorSearchResults([]);
      return;
    }

    try {
      setIsSearchingAuthors(true);
      const response = await searchUsers(query);
      setAuthorSearchResults(response.data);
    } catch (error) {
      console.error('Error searching authors:', error);
    } finally {
      setIsSearchingAuthors(false);
    }
  };

  // Handle author search input
  const handleAuthorSearch = (query: string) => {
    setAuthorSearchQuery(query);
    setShowAuthorDropdown(true);
    searchAuthors(query);
  };

  // Select author
  const selectAuthor = (author: User) => {
    setSelectedAuthor(author);
    setAuthorSearchQuery(author.name);
    setShowAuthorDropdown(false);
  };

  // Clear author selection
  const clearAuthor = () => {
    setSelectedAuthor(null);
    setAuthorSearchQuery('');
    setAuthorSearchResults([]);
  };

  // Handle image change
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Error",
          description: "Image size must be less than 5MB",
          variant: "destructive",
        });
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Error",
          description: "Only image files are allowed",
          variant: "destructive",
        });
        return;
      }

      setFeaturedImage(file);

      const reader = new FileReader();
      reader.onloadend = () => {
        setFeaturedImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove featured image
  const removeFeaturedImage = () => {
    setFeaturedImage(null);
    setFeaturedImagePreview(null);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent, submitStatus: 'draft' | 'published' = status) => {
    e.preventDefault();

    // Validate form
    if (!title.trim()) {
      toast({
        title: "Error",
        description: "Title is required",
        variant: "destructive",
      });
      return;
    }

    if (!content.trim()) {
      toast({
        title: "Error",
        description: "Content is required",
        variant: "destructive",
      });
      return;
    }

    if (!excerpt.trim()) {
      toast({
        title: "Error",
        description: "Excerpt is required",
        variant: "destructive",
      });
      return;
    }

    if (!selectedAuthor) {
      toast({
        title: "Error",
        description: "Please select an author",
        variant: "destructive",
      });
      return;
    }

    // Check for author ID - handle both _id and id fields
    const authorId = selectedAuthor._id || selectedAuthor.id;
    if (!authorId) {
      console.error('Selected author object:', selectedAuthor);
      toast({
        title: "Error",
        description: "Selected author is invalid. Please select an author again.",
        variant: "destructive",
      });
      return;
    }

    if (!category) {
      toast({
        title: "Error",
        description: "Please select a category",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      // Create FormData for API call
      const formData = new FormData();
      formData.append('title', title.trim());
      formData.append('content', content.trim());
      formData.append('excerpt', excerpt.trim());
      formData.append('author', authorId); // Use the extracted authorId
      formData.append('category', category);
      formData.append('tags', tags.trim());
      formData.append('status', submitStatus);

      if (publishDate) {
        formData.append('publishDate', publishDate.toISOString());
      }

      // Only include featured image if a new one is selected
      if (featuredImage) {
        formData.append('featuredImage', featuredImage);
      }

      let response;
      if (isEdit && id) {
        response = await updateBlog(id, formData);
      } else {
        response = await createBlog(formData);
      }

      toast({
        title: "Success",
        description: `Blog ${isEdit ? 'updated' : 'created'} successfully`,
      });

      navigate('/admin/blog');

    } catch (error: any) {
      console.error('Error saving blog:', error);
      toast({
        title: "Error",
        description: error.response?.data?.error || `Failed to ${isEdit ? 'update' : 'create'} blog`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    const loadData = async () => {
      await fetchCategories();
      if (isEdit) {
        await fetchBlogData();
      }
    };
    loadData();
  }, []);

  // Handle click outside for author dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.author-search-container')) {
        setShowAuthorDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (initialLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading blog data...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold">{isEdit ? 'Edit' : 'Add New'} Blog Post</h1>
            <p className="text-gray-500">{isEdit ? 'Update the' : 'Create a new'} blog post for your website</p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button
              variant="outline"
              onClick={(e) => handleSubmit(e, 'draft')}
              disabled={loading}
            >
              {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Save as Draft
            </Button>
            <Button
              onClick={(e) => handleSubmit(e, 'published')}
              disabled={loading}
            >
              {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Publish
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Main content column */}
            <div className="md:col-span-2 space-y-6">
              <div className="bg-white p-6 rounded-lg border shadow-sm">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      placeholder="Enter blog post title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      className="mt-1"
                      maxLength={200}
                    />
                    <p className="text-xs text-gray-500 mt-1">{title.length}/200 characters</p>
                  </div>

                  <div>
                    <Label htmlFor="excerpt">Excerpt</Label>
                    <Textarea
                      id="excerpt"
                      placeholder="Brief summary of the blog post"
                      rows={3}
                      value={excerpt}
                      onChange={(e) => setExcerpt(e.target.value)}
                      className="mt-1 resize-none"
                      maxLength={500}
                    />
                    <p className="text-xs text-gray-500 mt-1">{excerpt.length}/500 characters</p>
                  </div>

                  <div>
                    <Label htmlFor="content">Content</Label>
                    <WysiwygEditor
                      value={content}
                      onChange={(value) => setContent(value)}
                      placeholder="Write your blog post content here..."
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {content.replace(/<[^>]*>/g, '').split(' ').filter(word => word.length > 0).length} words
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg border shadow-sm">
                <h2 className="text-lg font-semibold mb-4">Featured Image</h2>
                <div className="space-y-4">
                  <div className="border-2 border-dashed rounded-lg p-4 text-center">
                    {featuredImagePreview ? (
                      <div className="relative">
                        <img
                          src={featuredImagePreview}
                          alt="Featured Preview"
                          className="max-h-64 mx-auto rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={removeFeaturedImage}
                        >
                          Remove
                        </Button>
                      </div>
                    ) : (
                      <div>
                        <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-sm text-gray-500">
                          Drag and drop an image here, or click to select a file
                        </p>
                        <Input
                          id="featured-image"
                          type="file"
                          accept="image/*"
                          className="mt-4 mx-auto max-w-xs"
                          onChange={handleImageChange}
                        />
                        <p className="text-xs text-gray-400 mt-2">Maximum file size: 5MB</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar column */}
            <div className="space-y-6">
              {/* Author Selection */}
              <div className="bg-white p-6 rounded-lg border shadow-sm">
                <h2 className="text-lg font-semibold mb-4">Author</h2>
                <div className="author-search-container relative">
                  <Label>Select Author</Label>
                  <div className="relative mt-1">
                    <div className="flex">
                      <Input
                        placeholder="Search for author..."
                        value={authorSearchQuery}
                        onChange={(e) => handleAuthorSearch(e.target.value)}
                        onFocus={() => setShowAuthorDropdown(true)}
                        className="pr-8"
                      />
                      {selectedAuthor && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                          onClick={clearAuthor}
                        >
                          <X size={12} />
                        </Button>
                      )}
                    </div>

                    {selectedAuthor && (
                      <div className="mt-2 p-2 bg-gray-50 rounded border">
                        <div className="font-medium">{selectedAuthor.name}</div>
                        <div className="text-sm text-gray-500">{selectedAuthor.email}</div>
                      </div>
                    )}

                    {showAuthorDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-auto">
                        {isSearchingAuthors ? (
                          <div className="p-3 text-center">
                            <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                            <span className="text-sm text-gray-500 ml-2">Searching...</span>
                          </div>
                        ) : authorSearchResults.length > 0 ? (
                          authorSearchResults.map((user) => (
                            <button
                              key={user._id}
                              type="button"
                              className="w-full px-3 py-2 text-left hover:bg-gray-50 border-b last:border-b-0"
                              onClick={() => selectAuthor(user)}
                            >
                              <div className="font-medium">{user.name}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </button>
                          ))
                        ) : authorSearchQuery.length >= 2 ? (
                          <div className="p-3 text-center text-gray-500">
                            No users found
                          </div>
                        ) : (
                          <div className="p-3 text-center text-gray-500">
                            Type at least 2 characters to search
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg border shadow-sm">
                <h2 className="text-lg font-semibold mb-4">Publish</h2>
                <div className="space-y-4">
                  <div>
                    <Label>Publish Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          type="button"
                          variant="outline"
                          className="w-full justify-start text-left font-normal mt-1"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {publishDate ? format(publishDate, "dd-MM-yyyy") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={publishDate}
                          onSelect={setPublishDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg border shadow-sm">
                <h2 className="text-lg font-semibold mb-4">Category</h2>
                {categories.length > 0 ? (
                  <Select
                    key={`select-${categories.length}`}
                    value={category}
                    onValueChange={(value) => {
                      setCategory(String(value));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((cat, index) => {
                        return (
                          <SelectItem key={`${cat._id}-${index}`} value={String(cat._id)}>
                            {cat.name}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex items-center justify-center p-4 border rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm text-gray-500">Loading categories...</span>
                  </div>
                )}
              </div>

              <div className="bg-white p-6 rounded-lg border shadow-sm">
                <h2 className="text-lg font-semibold mb-4">Tags</h2>
                <div>
                  <Input
                    placeholder="Enter tags separated by commas"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Enter tags separated by commas (e.g. business, startup, tech)
                  </p>
                </div>
              </div>

            </div>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default BlogAdd;
