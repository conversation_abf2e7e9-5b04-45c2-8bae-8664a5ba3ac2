import { SEOProps } from '@/components/SEO';

export interface StoryData {
  _id: string;
  title: string;
  excerpt: string;
  content: string;
  featuredImage?: string;
  author: {
    name: string;
    username: string;
  };
  category: {
    name: string;
  };
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface BlogData {
  _id: string;
  title: string;
  excerpt: string;
  content: string;
  featuredImage?: {
    url?: string;
    path?: string;
  };
  author: {
    name: string;
    username: string;
  };
  category: {
    name: string;
  };
  tags: string[];
  publishDate: string;
  updatedAt: string;
}

export interface UserData {
  _id: string;
  name: string;
  username: string;
  bio?: string;
  avatar?: string;
  roleTitle?: string;
  location?: string;
}

export interface CategoryData {
  _id: string;
  name: string;
  description: string;
}

export interface CaseStudyData {
  _id: string;
  title: string;
  subheading: string;
  introduction: string;
  featuredImage?: {
    filename?: string;
    originalname?: string;
    mimetype?: string;
    size?: number;
    path?: string;
    url?: string;
  };
  author: {
    name: string;
    username: string;
  };
  tags: string[];
  publishDate: string;
  updatedAt: string;
  startupSnapshot: {
    businessType: string;
    fundingStatus: string;
  };
}

// Generate SEO for story pages
export const generateStorySEO = (story: StoryData): SEOProps => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  const storyUrl = `${siteUrl}/story/${story._id}`;
  
  // Extract first 160 characters for description
  const description = story.excerpt || story.content.replace(/<[^>]*>/g, '').substring(0, 160) + '...';
  
  // Generate keywords from tags and category
  const keywords = [
    ...story.tags,
    story.category.name,
    'entrepreneur story',
    'startup success',
    'founder journey'
  ].join(', ');

  const imageUrl = story.featuredImage 
    ? (story.featuredImage.startsWith('http') ? story.featuredImage : `${siteUrl}/${story.featuredImage}`)
    : `${siteUrl}/uploads/default-story.jpg`;

  return {
    title: `${story.title} | StartupStories.io`,
    description,
    keywords,
    image: imageUrl,
    url: storyUrl,
    type: 'article',
    author: story.author.name,
    publishedTime: new Date(story.createdAt).toISOString(),
    modifiedTime: new Date(story.updatedAt).toISOString(),
    section: story.category.name,
    tags: story.tags,
    canonical: storyUrl
  };
};

// Generate SEO for blog pages
export const generateBlogSEO = (blog: BlogData): SEOProps => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  const blogUrl = `${siteUrl}/blog/${blog._id}`;
  
  const description = blog.excerpt || blog.content.replace(/<[^>]*>/g, '').substring(0, 160) + '...';
  
  const keywords = [
    ...blog.tags,
    blog.category.name,
    'startup blog',
    'entrepreneur insights',
    'business advice'
  ].join(', ');

  const imageUrl = blog.featuredImage?.url || blog.featuredImage?.path
    ? (blog.featuredImage.url?.startsWith('http') ? blog.featuredImage.url : `${siteUrl}/${blog.featuredImage.path}`)
    : `${siteUrl}/uploads/default-blog.jpg`;

  return {
    title: `${blog.title} | StartupStories.io Blog`,
    description,
    keywords,
    image: imageUrl,
    url: blogUrl,
    type: 'article',
    author: blog.author.name,
    publishedTime: new Date(blog.publishDate).toISOString(),
    modifiedTime: new Date(blog.updatedAt).toISOString(),
    section: blog.category.name,
    tags: blog.tags,
    canonical: blogUrl
  };
};

// Generate SEO for author/user profile pages
export const generateAuthorSEO = (user: UserData): SEOProps => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  const authorUrl = `${siteUrl}/author/@${user.username}`;
  
  const description = user.bio 
    ? `${user.bio.substring(0, 160)}...`
    : `Read stories and insights from ${user.name}, a tech entrepreneur sharing their journey on StartupStories.io.`;
  
  const keywords = [
    user.name,
    user.username,
    'entrepreneur profile',
    'founder profile',
    'startup founder'
  ].join(', ');

  const imageUrl = user.avatar 
    ? (user.avatar.startsWith('http') ? user.avatar : `${siteUrl}/${user.avatar}`)
    : `${siteUrl}/uploads/default-avatar.jpg`;

  return {
    title: `${user.name} (@${user.username}) | StartupStories.io`,
    description,
    keywords,
    image: imageUrl,
    url: authorUrl,
    type: 'profile',
    author: user.name,
    canonical: authorUrl
  };
};

// Generate SEO for category pages
export const generateCategorySEO = (category: CategoryData): SEOProps => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  const categoryUrl = `${siteUrl}/category/${category._id}`;
  
  const description = category.description 
    ? `${category.description.substring(0, 160)}...`
    : `Explore ${category.name} stories from successful entrepreneurs and startup founders on StartupStories.io.`;
  
  const keywords = [
    category.name,
    'entrepreneur stories',
    'startup category',
    'founder insights'
  ].join(', ');

  return {
    title: `${category.name} Stories | StartupStories.io`,
    description,
    keywords,
    image: `${siteUrl}/uploads/category-${category.name.toLowerCase().replace(/\s+/g, '-')}.jpg`,
    url: categoryUrl,
    type: 'website',
    canonical: categoryUrl
  };
};

// Generate SEO for static pages
export const generatePageSEO = (pageType: string): SEOProps => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  
  console.log('generatePageSEO called with:', { pageType, siteUrl });
  
  const pageConfigs: Record<string, Partial<SEOProps>> = {
    home: {
      title: 'StartupStories.io: Real Startup Stories, Founder Insights & SaaS Case Studies',
      description: 'Discover real startup stories, actionable founder case studies, and SaaS growth lessons. Join StartupStories.io to learn, connect, and grow your business journey.',
      keywords: 'startup stories, founder stories, SaaS case studies, tech startups, entrepreneurship, business inspiration, founder interviews, startup community, startup growth, SaaS growth, build in public, startup tips',
      url: siteUrl,
      type: 'website'
    },
    stories: {
      title: 'Entrepreneur Stories | StartupStories.io',
      description: 'Browse inspiring stories from successful tech entrepreneurs and startup founders. Learn from their challenges, failures, and triumphs.',
      keywords: 'entrepreneur stories, startup stories, founder journeys, business success stories',
      url: `${siteUrl}/stories`,
      type: 'website'
    },
    blog: {
      title: 'Startup Blog | StartupStories.io',
      description: 'Read the latest insights, tips, and advice for entrepreneurs and startup founders. Expert content to help you build and scale your business.',
      keywords: 'startup blog, entrepreneur advice, business tips, startup insights',
      url: `${siteUrl}/blog`,
      type: 'website'
    },
    'case-studies': {
      title: 'Startup Case Studies | StartupStories.io',
      description: 'Explore detailed case studies of successful startups and entrepreneurs. Learn from their strategies, challenges, and growth tactics.',
      keywords: 'startup case studies, entrepreneur case studies, business analysis, startup strategies, growth tactics',
      url: `${siteUrl}/case-studies`,
      type: 'website'
    },
    categories: {
      title: 'Story Categories | StartupStories.io',
      description: 'Explore entrepreneur stories organized by industry, stage, and topic. Find relevant insights for your startup journey.',
      keywords: 'story categories, startup industries, entrepreneur topics',
      url: `${siteUrl}/categories`,
      type: 'website'
    },
    directory: {
      title: 'Founder Directory | StartupStories.io',
      description: 'Connect with successful entrepreneurs and startup founders. Browse profiles of tech leaders sharing their stories.',
      keywords: 'founder directory, entrepreneur network, startup founders',
      url: `${siteUrl}/directory`,
      type: 'website'
    },
    academy: {
      title: 'Startup Academy | StartupStories.io',
      description: 'Learn from expert courses and resources designed for entrepreneurs. Build the skills you need to succeed.',
      keywords: 'startup academy, entrepreneur education, business courses',
      url: `${siteUrl}/academy`,
      type: 'website'
    },
    'subscription-plans': {
      title: 'Premium Plans | StartupStories.io',
      description: 'Unlock premium entrepreneur stories and exclusive content. Join our community of successful founders.',
      keywords: 'premium stories, entrepreneur membership, startup community',
      url: `${siteUrl}/subscription-plans`,
      type: 'website'
    }
  };

  const result = {
    ...pageConfigs[pageType],
    canonical: pageConfigs[pageType]?.url
  } as SEOProps;
  
  console.log('generatePageSEO result:', result);
  
  return result;
};

// Generate SEO for case study pages
export const generateCaseStudySEO = (caseStudy: CaseStudyData): SEOProps => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  const caseStudyUrl = `${siteUrl}/case-study/${caseStudy._id}`;
  
  const description = caseStudy.subheading || caseStudy.introduction.substring(0, 160) + '...';
  
  const keywords = [
    ...caseStudy.tags,
    caseStudy.startupSnapshot.businessType,
    'case study',
    'startup case study',
    'entrepreneur insights',
    'business analysis'
  ].join(', ');

  const imageUrl = caseStudy.featuredImage?.path || caseStudy.featuredImage?.url
    ? (caseStudy.featuredImage.url?.startsWith('http') ? caseStudy.featuredImage.url : `${siteUrl}/${caseStudy.featuredImage.path}`)
    : `${siteUrl}/uploads/default-case-study.jpg`;

  return {
    title: `${caseStudy.title} | StartupStories.io Case Studies`,
    description,
    keywords,
    image: imageUrl,
    url: caseStudyUrl,
    type: 'article',
    author: caseStudy.author.name,
    publishedTime: new Date(caseStudy.publishDate).toISOString(),
    modifiedTime: new Date(caseStudy.updatedAt).toISOString(),
    section: 'Case Studies',
    tags: caseStudy.tags,
    canonical: caseStudyUrl
  };
};

// Generate structured data for stories
export const generateStoryStructuredData = (story: StoryData) => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": story.title,
    "description": story.excerpt,
    "image": story.featuredImage ? `${siteUrl}/${story.featuredImage}` : `${siteUrl}/uploads/default-story.jpg`,
    "author": {
      "@type": "Person",
      "name": story.author.name,
      "url": `${siteUrl}/author/@${story.author.username}`
    },
    "publisher": {
      "@type": "Organization",
      "name": "StartupStories.io",
      "logo": {
        "@type": "ImageObject",
        "url": `${siteUrl}/logo.png`
      }
    },
    "datePublished": new Date(story.createdAt).toISOString(),
    "dateModified": new Date(story.updatedAt).toISOString(),
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${siteUrl}/story/${story._id}`
    },
    "articleSection": story.category.name,
    "keywords": story.tags.join(', ')
  };
};

// Generate structured data for case studies
export const generateCaseStudyStructuredData = (caseStudy: CaseStudyData) => {
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:8080';
  
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": caseStudy.title,
    "description": caseStudy.subheading,
    "image": caseStudy.featuredImage?.path ? `${siteUrl}/${caseStudy.featuredImage.path}` : `${siteUrl}/uploads/default-case-study.jpg`,
    "author": {
      "@type": "Person",
      "name": caseStudy.author.name,
      "url": `${siteUrl}/author/@${caseStudy.author.username}`
    },
    "publisher": {
      "@type": "Organization",
      "name": "StartupStories.io",
      "logo": {
        "@type": "ImageObject",
        "url": `${siteUrl}/logo.png`
      }
    },
    "datePublished": new Date(caseStudy.publishDate).toISOString(),
    "dateModified": new Date(caseStudy.updatedAt).toISOString(),
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${siteUrl}/case-study/${caseStudy._id}`
    },
    "articleSection": "Case Studies",
    "keywords": caseStudy.tags.join(', '),
    "about": {
      "@type": "Organization",
      "name": caseStudy.title.split(' ')[1] || "Startup",
      "description": caseStudy.introduction
    }
  };
}; 