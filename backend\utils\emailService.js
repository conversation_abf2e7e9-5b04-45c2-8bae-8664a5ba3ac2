const nodemailer = require('nodemailer');

// Create reusable transporter object using SMTP transport
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_EMAIL,
      pass: process.env.SMTP_PASSWORD,
    },
  });
};

// Send welcome email
const sendWelcomeEmail = async (email, name) => {
  const transporter = createTransporter();

  const message = {
    from: `"${process.env.FROM_NAME || 'StartupStories.io'}" <${process.env.FROM_EMAIL || process.env.SMTP_EMAIL}>`,
    to: email,
    subject: 'Welcome to StartupStories.io!',
    html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to StartupStories.io!</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #f3f4f6; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
">
  
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f3f4f6;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Welcome to StartupStories.io!</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Join our founder community</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hi ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 32px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Thanks for joining our founder community! Dive into real startup journeys, share your own story, and connect with others building amazing things. We're excited to see what you'll create here.</p>
              
              <!-- CTA Button -->
              <div style="text-align: center; margin: 40px 0;">
                <a href="${process.env.FRONTEND_URL}" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">Start Exploring</a>
              </div>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #f8fafc; 
              border-top: 1px solid #e2e8f0; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">The StartupStories.io Team</p>
              
              <!-- Social Media Icons -->
              <div style="margin: 24px 0;">
                <a href="https://x.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #1DA1F2;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">𝕏</span>
                </a>
                <a href="https://linkedin.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #0077B5;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  "><i class="fab fa-linkedin-in" style="line-height: inherit;">in</i></span>
                </a>
                <a href="https://instagram.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">📷</span>
                </a>
                <a href="https://reddit.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF4500;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">r</span>
                </a>
                <a href="https://youtube.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF0000;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">▶</span>
                </a>
              </div>
              
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} StartupStories.io. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
</body>
</html>`,
  };

  try {
    await transporter.sendMail(message);
    console.log('Welcome email sent successfully');
  } catch (error) {
    console.error('Error sending welcome email:', error);
    throw new Error('Welcome email could not be sent');
  }
};

// Send premium subscription confirmation email for case-study
const sendPremiumSubscriptionConfirmationEmail = async (email, name) => {
  const transporter = createTransporter();

  const message = {
    from: `"${process.env.FROM_NAME || 'StartupStories.io'}" <${process.env.FROM_EMAIL || process.env.SMTP_EMAIL}>`,
    to: email,
    subject: 'Welcome to Premium – Unlimited Access Unlocked',
    html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Premium – Unlimited Access Unlocked</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #f3f4f6; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
">
  
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f3f4f6;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Welcome to Premium</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Unlimited access unlocked</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hi ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 32px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Thanks for becoming a Premium member! You now have full access to all premium case studies, insights, and founder tools. Enjoy exploring!</p>
              
              <!-- CTA Button -->
              <div style="text-align: center; margin: 40px 0;">
                <a href="${process.env.FRONTEND_URL}/case-studies" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">Explore Case Studies</a>
              </div>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #f8fafc; 
              border-top: 1px solid #e2e8f0; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">The StartupStories.io Team</p>
              
              <!-- Social Media Icons -->
              <div style="margin: 24px 0;">
                <a href="https://x.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #1DA1F2;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">𝕏</span>
                </a>
                <a href="https://linkedin.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #0077B5;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  "><i class="fab fa-linkedin-in" style="line-height: inherit;">in</i></span>
                </a>
                <a href="https://instagram.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">📷</span>
                </a>
                <a href="https://reddit.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF4500;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">r</span>
                </a>
                <a href="https://youtube.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF0000;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">▶</span>
                </a>
              </div>
              
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} StartupStories.io. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
</body>
</html>`,
  };

  try {
    await transporter.sendMail(message);
    console.log('Premium subscription confirmation email sent successfully');
  } catch (error) {
    console.error('Error sending premium subscription confirmation email:', error);
    throw new Error('Premium subscription confirmation email could not be sent');
  }
};

// Send profile verified confirmation email
const sendProfileVerifiedConfirmationEmail = async (email, name) => {
  const transporter = createTransporter();

  const message = {
    from: `"${process.env.FROM_NAME || 'StartupStories.io'}" <${process.env.FROM_EMAIL || process.env.SMTP_EMAIL}>`,
    to: email,
    subject: 'Congratulations, You\'re Now a Verified Founder',
    html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Congratulations, You're Now a Verified Founder</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #f3f4f6; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
">
  
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f3f4f6;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">You're Now Verified!</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Stand out as a verified founder</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hey ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Your payment has been received and your profile is officially verified!</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 32px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Your startup story and founder details will now stand out to our entire community. Log in anytime to edit or enhance your profile and maximize your visibility.</p>
              
              <!-- CTA Button -->
              <div style="text-align: center; margin: 40px 0;">
                <a href="${process.env.FRONTEND_URL}/profile" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">View Your Profile</a>
              </div>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 24px 0 0 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Thank you for being a valued part of StartupStories.io!</p>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #f8fafc; 
              border-top: 1px solid #e2e8f0; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">The StartupStories.io Team</p>
              
              <!-- Social Media Icons -->
              <div style="margin: 24px 0;">
                <a href="https://x.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #1DA1F2;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">𝕏</span>
                </a>
                <a href="https://linkedin.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #0077B5;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  "><i class="fab fa-linkedin-in" style="line-height: inherit;">in</i></span>
                </a>
                <a href="https://instagram.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">📷</span>
                </a>
                <a href="https://reddit.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF4500;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">r</span>
                </a>
                <a href="https://youtube.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF0000;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">▶</span>
                </a>
              </div>
              
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} StartupStories.io. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
</body>
</html>`,
  };

  try {
    await transporter.sendMail(message);
    console.log('Profile verified confirmation email sent successfully');
  } catch (error) {
    console.error('Error sending profile verified confirmation email:', error);
    throw new Error('Profile verified confirmation email could not be sent');
  }
};

// Send story submission confirmation email
const sendStorySubmissionConfirmationEmail = async (email, name, storyTitle) => {
  const transporter = createTransporter();

  const message = {
    from: `"${process.env.FROM_NAME || 'StartupStories.io'}" <${process.env.FROM_EMAIL || process.env.SMTP_EMAIL}>`,
    to: email,
    subject: 'We\'ve Received Your Story!',
    html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>We've Received Your Story!</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #f3f4f6; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
">
  
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f3f4f6;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Story received!</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Your submission is under review</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hi ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Thanks for sharing your founder journey. Our team will review your story "${storyTitle}" and notify you once it's published.</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 32px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Excited to read your experience!</p>
              
              <!-- CTA Button -->
              <div style="text-align: center; margin: 40px 0;">
                <a href="${process.env.FRONTEND_URL}/my-stories" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">View My Stories</a>
              </div>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #f8fafc; 
              border-top: 1px solid #e2e8f0; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">The StartupStories.io Team</p>
              
              <!-- Social Media Icons -->
              <div style="margin: 24px 0;">
                <a href="https://x.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #1DA1F2;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">𝕏</span>
                </a>
                <a href="https://linkedin.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #0077B5;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  "><i class="fab fa-linkedin-in" style="line-height: inherit;">in</i></span>
                </a>
                <a href="https://instagram.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">📷</span>
                </a>
                <a href="https://reddit.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF4500;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">r</span>
                </a>
                <a href="https://youtube.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF0000;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">▶</span>
                </a>
              </div>
              
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} StartupStories.io. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
</body>
</html>`,
  };

  try {
    await transporter.sendMail(message);
    console.log('Story submission confirmation email sent successfully');
  } catch (error) {
    console.error('Error sending story submission confirmation email:', error);
    throw new Error('Story submission confirmation email could not be sent');
  }
};

// Send story published notification email
const sendStoryPublishedNotificationEmail = async (email, name, storyTitle, storySlug) => {
  const transporter = createTransporter();

  const storyUrl = `${process.env.FRONTEND_URL}/story/${storySlug}`;

  const message = {
    from: `"${process.env.FROM_NAME || 'StartupStories.io'}" <${process.env.FROM_EMAIL || process.env.SMTP_EMAIL}>`,
    to: email,
    subject: 'Your Startup Story Is Live!',
    html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your Startup Story Is Live!</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #f3f4f6; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
">
  
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f3f4f6;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Your Story Is Live!</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Congratulations on sharing your journey</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hi ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Good news—your story "${storyTitle}" is now published on StartupStories.io! Share your unique journey with the community and celebrate your milestone.</p>
              
              <!-- CTA Button -->
              <div style="text-align: center; margin: 40px 0;">
                <a href="${storyUrl}" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">View Your Story</a>
              </div>
              
              <!-- Alternative Link -->
              <div style="
                background-color: #f9fafb; 
                border: 1px solid #e5e7eb;
                border-radius: 12px; 
                padding: 24px; 
                margin: 32px 0;
              ">
                <p style="
                  color: #6b7280; 
                  font-size: 14px; 
                  margin: 0 0 12px 0; 
                  font-weight: 500;
                ">Share your story with this link:</p>
                
                <p style="
                  color: #6366f1; 
                  font-size: 14px; 
                  word-break: break-all; 
                  margin: 0; 
                  font-family: 'SF Mono', Monaco, Consolas, monospace;
                  background-color: #ffffff;
                  padding: 12px;
                  border-radius: 6px;
                  border: 1px solid #e5e7eb;
                "><a href="${storyUrl}" style="color: #6366f1; text-decoration: none;">${storyUrl}</a></p>
              </div>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #f8fafc; 
              border-top: 1px solid #e2e8f0; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">The StartupStories.io Team</p>
              
              <!-- Social Media Icons -->
              <div style="margin: 24px 0;">
                <a href="https://x.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #1DA1F2;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">𝕏</span>
                </a>
                <a href="https://linkedin.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #0077B5;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  "><i class="fab fa-linkedin-in" style="line-height: inherit;">in</i></span>
                </a>
                <a href="https://instagram.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">📷</span>
                </a>
                <a href="https://reddit.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF4500;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">r</span>
                </a>
                <a href="https://youtube.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF0000;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">▶</span>
                </a>
              </div>
              
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} StartupStories.io. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
</body>
</html>`,
  };

  try {
    await transporter.sendMail(message);
    console.log('Story published notification email sent successfully');
  } catch (error) {
    console.error('Error sending story published notification email:', error);
    throw new Error('Story published notification email could not be sent');
  }
};

// Send password reset email
const sendPasswordResetEmail = async (email, resetToken) => {
  const transporter = createTransporter();

  const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

  const message = {
    from: `"${process.env.FROM_NAME || 'Founder Story'}" <${process.env.FROM_EMAIL || process.env.SMTP_EMAIL}>`,
    to: email,
    subject: 'Password Reset Request',
    html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Reset Request</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #f3f4f6; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
">
  
  <!-- Email Container -->
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f3f4f6;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <!-- Main Content Card -->
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Password Reset Request</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Secure your Founder Story account</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hello!</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 32px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">You have requested to reset your password for your Founder Story account. Click the button below to reset your password:</p>
              
              <!-- Reset Button -->
              <div style="text-align: center; margin: 40px 0;">
                <!--[if mso]>
                <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${resetUrl}" style="height:56px;v-text-anchor:middle;width:240px;" arcsize="21%" stroke="f" fillcolor="#6366f1">
                <w:anchorlock/>
                <center style="color:#ffffff;font-family:sans-serif;font-size:16px;font-weight:600;">Reset Password</center>
                </v:roundrect>
                <![endif]-->
                <!--[if !mso]><!-->
                <a href="${resetUrl}" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">Reset Password</a>
                <!--<![endif]-->
              </div>
              
              <!-- Alternative Link -->
              <div style="
                background-color: #f9fafb; 
                border: 1px solid #e5e7eb;
                border-radius: 12px; 
                padding: 24px; 
                margin: 32px 0;
              ">
                <p style="
                  color: #6b7280; 
                  font-size: 14px; 
                  margin: 0 0 12px 0; 
                  font-weight: 500;
                ">If the button doesn't work, copy and paste this link into your browser:</p>
                
                <p style="
                  color: #6366f1; 
                  font-size: 14px; 
                  word-break: break-all; 
                  margin: 0; 
                  font-family: 'SF Mono', Monaco, Consolas, monospace;
                  background-color: #ffffff;
                  padding: 12px;
                  border-radius: 6px;
                  border: 1px solid #e5e7eb;
                "><a href="${resetUrl}" style="color: #6366f1; text-decoration: none;">${resetUrl}</a></p>
              </div>
              
              <!-- Security Notice -->
              <div style="
                border-left: 4px solid #f59e0b; 
                background-color: #fffbeb; 
                padding: 20px; 
                border-radius: 0 8px 8px 0; 
                margin: 32px 0;
              ">
                <p style="
                  color: #92400e; 
                  font-size: 14px; 
                  margin: 0 0 8px 0; 
                  font-weight: 600;
                ">⚠️ Important Security Notice</p>
                <p style="
                  color: #a16207; 
                  font-size: 14px; 
                  margin: 0; 
                  line-height: 1.5;
                ">This link will expire in 10 minutes for security reasons. If you did not request this password reset, please ignore this email and your password will remain unchanged.</p>
              </div>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #f8fafc; 
              border-top: 1px solid #e2e8f0; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Thanks,<br><strong style="color: #374151;">The Founder Story Team</strong></p>
              
              <!-- Social Media Icons -->
              <div style="margin: 24px 0;">
                <a href="https://x.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #1DA1F2;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">𝕏</span>
                </a>
                <a href="https://linkedin.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #0077B5;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  "><i class="fab fa-linkedin-in" style="line-height: inherit;">in</i></span>
                </a>
                <a href="https://instagram.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">📷</span>
                </a>
                <a href="https://reddit.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF4500;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">r</span>
                </a>
                <a href="https://youtube.com" style="text-decoration: none; margin: 0 8px;">
                  <span style="
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    background-color: #FF0000;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    color: white;
                    font-size: 16px;
                  ">▶</span>
                </a>
              </div>
              
              <!-- Copyright -->
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} Founder Story. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
  <!-- Mobile Styles -->
  <style>
    @media only screen and (max-width: 600px) {
      .email-container {
        padding: 20px 16px !important;
      }
      .content-padding {
        padding: 32px 24px !important;
      }
      .header-padding {
        padding: 32px 24px 24px !important;
      }
      .footer-padding {
        padding: 24px !important;
      }
      .button-container {
        margin: 32px 0 !important;
      }
      .reset-button {
        padding: 14px 32px !important;
        font-size: 15px !important;
        min-width: 180px !important;
      }
      .title {
        font-size: 28px !important;
      }
      .subtitle {
        font-size: 15px !important;
      }
    }
  </style>
  
</body>
</html>`,
  };

  try {
    await transporter.sendMail(message);
    console.log('Password reset email sent successfully');
  } catch (error) {
    console.error('Error sending email:', error);
    throw new Error('Email could not be sent');
  }
};


module.exports = {
  sendPasswordResetEmail,
  sendWelcomeEmail,
  sendPremiumSubscriptionConfirmationEmail,
  sendProfileVerifiedConfirmationEmail,
  sendStorySubmissionConfirmationEmail,
  sendStoryPublishedNotificationEmail,
};
