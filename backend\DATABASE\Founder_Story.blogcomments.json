[{"_id": {"$oid": "6833fbb45a5858f135b7d0f4"}, "content": "<PERSON><PERSON><PERSON> accuses leaders of Britain, France and Canada of ‘emboldening Hamas", "author": {"$oid": "682ad9136ca3666a54bea9fa"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": null, "likes": 2, "likedBy": [{"$oid": "682ad9136ca3666a54bea9fa"}, {"$oid": "682eeb5d30ac52ab48e0270e"}], "isEdited": false, "editedAt": null, "status": "active", "createdAt": {"$date": "2025-05-26T05:27:16.584Z"}, "updatedAt": {"$date": "2025-05-26T05:29:27.134Z"}, "__v": 2}, {"_id": {"$oid": "6833fbd05a5858f135b7d10c"}, "content": "A passionate writer and expert in their field, sharing insights and knowledge through engaging content.", "author": {"$oid": "682eeb5d30ac52ab48e0270e"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": null, "likes": 0, "likedBy": [], "isEdited": false, "editedAt": null, "status": "active", "createdAt": {"$date": "2025-05-26T05:27:44.459Z"}, "updatedAt": {"$date": "2025-05-26T05:27:44.459Z"}, "__v": 0}, {"_id": {"$oid": "6833fbda5a5858f135b7d112"}, "content": "“I say to President <PERSON><PERSON>, prime minister <PERSON><PERSON> and prime minister <PERSON><PERSON>, when mass murderers, rapists, baby killers and kidnappers thank you, you’re on the wrong side of justice.<PERSON> <PERSON><PERSON><PERSON> said the actions of the leaders were not “advancing peace”, but “emboldening Hamas to continue fighting for ever”.", "author": {"$oid": "682eeb5d30ac52ab48e0270e"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": {"$oid": "6833fbb45a5858f135b7d0f4"}, "likes": 0, "likedBy": [], "isEdited": false, "editedAt": null, "status": "active", "createdAt": {"$date": "2025-05-26T05:27:54.573Z"}, "updatedAt": {"$date": "2025-05-26T05:27:54.573Z"}, "__v": 0}, {"_id": {"$oid": "6833fbe75a5858f135b7d118"}, "content": "[This comment has been deleted]", "author": {"$oid": "682eeb5d30ac52ab48e0270e"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": {"$oid": "6833fbb45a5858f135b7d0f4"}, "likes": 0, "likedBy": [], "isEdited": false, "editedAt": null, "status": "deleted", "createdAt": {"$date": "2025-05-26T05:28:07.551Z"}, "updatedAt": {"$date": "2025-05-26T05:28:12.926Z"}, "__v": 0}, {"_id": {"$oid": "6833fc1b5a5858f135b7d157"}, "content": "<PERSON> and <PERSON> of “emboldening Hamas”, after they called for a halt to Israel’s military offensive and an end to restrictions on humanitarian aid in Gaza. Earlier this week, the leaders of the UK, France and Canada condemned the Israeli government’s “egregious” actions in Gaza", "author": {"$oid": "682ad9136ca3666a54bea9fa"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": null, "likes": 0, "likedBy": [], "isEdited": false, "editedAt": null, "status": "active", "createdAt": {"$date": "2025-05-26T05:28:59.911Z"}, "updatedAt": {"$date": "2025-05-26T05:28:59.911Z"}, "__v": 0}, {"_id": {"$oid": "6833fc305a5858f135b7d15c"}, "content": "<PERSON> 23 May 2025 06.54 BST Share <PERSON>", "author": {"$oid": "682eeb5d30ac52ab48e0270e"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": null, "likes": 1, "likedBy": [{"$oid": "682ad9136ca3666a54bea9fa"}], "isEdited": false, "editedAt": null, "status": "active", "createdAt": {"$date": "2025-05-26T05:29:20.298Z"}, "updatedAt": {"$date": "2025-06-04T07:14:29.644Z"}, "__v": 1}, {"_id": {"$oid": "6833fc625a5858f135b7d185"}, "content": "<PERSON><PERSON><PERSON> accuses leaders of Britain, France and Canada of ‘emboldening Hamas", "author": {"$oid": "682eeb5d30ac52ab48e0270e"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": {"$oid": "6833fbd05a5858f135b7d10c"}, "likes": 0, "likedBy": [], "isEdited": false, "editedAt": null, "status": "active", "createdAt": {"$date": "2025-05-26T05:30:10.951Z"}, "updatedAt": {"$date": "2025-05-26T05:30:10.951Z"}, "__v": 0}, {"_id": {"$oid": "683407eb5a5858f135b7d387"}, "content": "hyyyyyyyyyyyy", "author": {"$oid": "682ad9136ca3666a54bea9fa"}, "blog": {"$oid": "6830674025a3344295d770d8"}, "parentComment": {"$oid": "6833fc305a5858f135b7d15c"}, "likes": 0, "likedBy": [], "isEdited": false, "editedAt": null, "status": "active", "createdAt": {"$date": "2025-05-26T06:19:23.138Z"}, "updatedAt": {"$date": "2025-05-26T06:19:23.138Z"}, "__v": 0}]