[{"_id": {"$oid": "682f0190909747acf01b191f"}, "user": {"$oid": "682ad9136ca3666a54bea9fa"}, "startupName": "NovaLearn", "websiteUrl": "https://liamtradescrypto.com", "tagline": "Empowering students with personalized, AI-driven learning paths", "logoUrl": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRENF9uv9UWIWWbExsgj7XyX58xMFAOZTzUSQ&s", "industry": "AI & ML", "shortDescription": "NovaLearn is an AI-powered education platform that provides personalized learning paths for high school and college students, helping them master subjects faster and more effectively.", "appStoreLink": "https://apps.apple.com/app/novalearn", "playStoreLink": "https://play.google.com/store/apps/details?id=novalearn.app", "foundedYear": "2024", "revenueModel": "Advertising", "fundingStage": "Series A", "keyMilestones": "50K+ registered users\nRaised $500K in seed round\nPartnered with 20+ educational institutions", "ctaButtonText": "Try Demo", "ctaButtonLink": "https://novalearn.io/assets/logo.png", "status": "approved", "rejectionFeedback": "", "createdAt": {"$date": "2025-05-22T10:50:56.551Z"}, "updatedAt": {"$date": "2025-05-22T10:50:56.551Z"}, "__v": 0}, {"_id": {"$oid": "6843b89460531e86892d30dd"}, "user": {"$oid": "6843b67d60531e86892d3086"}, "startupName": "TechNova Solutions", "websiteUrl": "https://technova.com", "tagline": "Innovating the future of technology", "logoUrl": "https://i.pinimg.com/564x/de/ac/df/deacdf0b639c035cdebefb3ec53aee2e.jpg", "industry": "Blockchain", "shortDescription": "TechNova provides cutting-edge AI solutions for businesses to automate their processes and increase efficiency. Our platform uses machine learning to analyze data and provide actionable insights", "appStoreLink": "https://play.google.com/technova", "playStoreLink": "https://apps.apple.com/technova", "foundedYear": "2006", "revenueModel": "Marketplace Commission", "fundingStage": "Bootstrapped", "keyMilestones": "Reached 10,000 users in first year, partnered with 5 Fortune 500 companies", "ctaButtonText": "Get Early Access", "ctaButtonLink": "https://technova.com/demo", "status": "approved", "rejectionFeedback": "", "createdAt": {"$date": "2025-06-07T03:57:08.213Z"}, "updatedAt": {"$date": "2025-06-07T03:57:08.213Z"}, "__v": 0}]