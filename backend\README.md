# Startup Stories Backend

Backend API for the Startup Stories platform, providing authentication, user profile management, and admin capabilities.

## Technologies Used

- Node.js
- Express
- MongoDB with Mongoose
- JWT Authentication
- Google OAuth Integration
- bcryptjs for password hashing

## Setup Instructions

1. Install dependencies:
   ```
   npm install
   ```

2. Configure environment variables:
   - Create a `.env` file in the root directory
   - Copy the variables from `.env.example` file
   - Update the variables with your own values

3. Run the server:
   - Development mode: `npm run dev`
   - Production mode: `npm start`

## API Documentation

### Authentication Endpoints

#### User Registration
- **URL**: `/api/auth/register`
- **Method**: POST
- **Body**:
  ```json
  {
    "name": "User Name",
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "token": "JWT_TOKEN",
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>",
      "role": "user",
      "avatar": null
    }
  }
  ```

#### User Login
- **URL**: `/api/auth/login`
- **Method**: POST
- **Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**: Same as Registration

#### Google Login
- **URL**: `/api/auth/google`
- **Method**: POST
- **Body**:
  ```json
  {
    "idToken": "GOOGLE_ID_TOKEN"
  }
  ```
- **Response**: Same as Registration

#### Get Current User
- **URL**: `/api/auth/me`
- **Method**: GET
- **Headers**: `Authorization: Bearer JWT_TOKEN`
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>",
      "role": "user",
      ...
    }
  }
  ```

#### Logout
- **URL**: `/api/auth/logout`
- **Method**: GET
- **Headers**: `Authorization: Bearer JWT_TOKEN`
- **Response**:
  ```json
  {
    "success": true,
    "data": {}
  }
  ```

### User Profile Endpoints

#### Get User Profile
- **URL**: `/api/users/profile`
- **Method**: GET
- **Headers**: `Authorization: Bearer JWT_TOKEN`
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>",
      "bio": "User bio",
      "location": "User location",
      "roleTitle": "Job title",
      "availability": "Availability status",
      ...
    }
  }
  ```

#### Update User Profile
- **URL**: `/api/users/profile`
- **Method**: PUT
- **Headers**: `Authorization: Bearer JWT_TOKEN`
- **Body**:
  ```json
  {
    "name": "Updated Name",
    "bio": "Updated bio",
    "location": "Updated location",
    "roleTitle": "Updated job title",
    "availability": "Updated availability"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "id": "user_id",
      "name": "Updated Name",
      "email": "<EMAIL>",
      "bio": "Updated bio",
      "location": "Updated location",
      "roleTitle": "Updated job title",
      "availability": "Updated availability",
      ...
    }
  }
  ```

### Admin Endpoints

#### Admin Login
- **URL**: `/api/admin/login`
- **Method**: POST
- **Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "adminpassword"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "token": "JWT_TOKEN",
    "admin": {
      "id": "admin_id",
      "name": "Admin Name",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
  ```

#### Get Admin Profile
- **URL**: `/api/admin/me`
- **Method**: GET
- **Headers**: `Authorization: Bearer JWT_TOKEN`
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "id": "admin_id",
      "name": "Admin Name",
      "email": "<EMAIL>",
      "role": "admin",
      "lastLogin": "2023-08-01T12:00:00.000Z",
      ...
    }
  }
  ```

#### Admin Logout
- **URL**: `/api/admin/logout`
- **Method**: GET
- **Headers**: `Authorization: Bearer JWT_TOKEN`
- **Response**:
  ```json
  {
    "success": true,
    "data": {}
  }
  ```

#### Create Initial Admin (Secure This in Production)
- **URL**: `/api/admin/create-initial`
- **Method**: POST
- **Body**:
  ```json
  {
    "name": "Admin Name",
    "email": "<EMAIL>",
    "password": "adminpassword",
    "secretKey": "YOUR_SECRET_KEY_FROM_ENV"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Initial admin created successfully"
  }
  ```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": "Error message"
}
```

For validation errors:

```json
{
  "success": false,
  "errors": [
    {
      "field": "email",
      "message": "Please include a valid email"
    }
  ]
}
``` 