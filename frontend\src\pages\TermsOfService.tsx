import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { ChevronUp } from 'lucide-react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const TermsOfService = () => {
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);

  // Scroll to top when component mounts
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Show button when page is scrolled down
  React.useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <h1 className="text-4xl font-bold mb-8 text-center">Terms of Service</h1>
          
          <div className="bg-white rounded-lg shadow-md p-6 md:p-8 space-y-6">
            <div className="text-gray-600 text-sm mb-6">
              Effective Date: June 11, 2025
            </div>
            
            <section>
              <p className="text-gray-700 mb-4">
                Welcome to Startup Stories! By accessing or using our website and services, you agree to these Terms and Conditions ("Terms"). Please read them carefully.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">1. Who Can Use Startup Stories</h2>
              <p className="text-gray-700 mb-3">
                You must be at least 18 years old to create an account or use our platform.
              </p>
              <p className="text-gray-700">
                By signing up, you confirm that all information you provide is accurate and current.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">2. Accounts and Security</h2>
              <p className="text-gray-700 mb-3">
                You are responsible for maintaining the confidentiality of your account and password.
              </p>
              <p className="text-gray-700">
                You agree to notify us immediately of any unauthorized use of your account.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">3. User-Generated Content</h2>
              <p className="text-gray-700 mb-3">
                By submitting stories, comments, or any other content, you grant Startup Stories a non-exclusive, worldwide, royalty-free right to use, display, promote, and, if applicable, include your content in paid or premium areas of the site.
              </p>
              <p className="text-gray-700 mb-3">
                We may edit, remove, or decline to publish content that violates our guidelines or is inappropriate.
              </p>
              <p className="text-gray-700">
                You are responsible for the content you post and must not post anything illegal, abusive, or in violation of someone else's rights.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">4. Use of Stories in Premium Memberships</h2>
              <p className="text-gray-700 mb-3">
                You agree that your submitted story may be included in our premium membership or paid sections, and other users may pay to access your story as part of our premium content.
              </p>
              <p className="text-gray-700">
                Your story will always be attributed to your name/profile as provided.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">5. Paid Plans and Refunds</h2>
              <p className="text-gray-700 mb-3">
                All fees for Profile Verification and Premium Membership are charged annually and are non-refundable.
              </p>
              <p className="text-gray-700 mb-3">
                Startup Stories does not offer any lifetime deals. All memberships renew annually unless canceled before renewal.
              </p>
              <p className="text-gray-700">
                Please review our Refund Policy before subscribing.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">6. Community Guidelines</h2>
              <p className="text-gray-700 mb-3">
                Be respectful. Harassment, spam, and abuse will not be tolerated.
              </p>
              <p className="text-gray-700 mb-3">
                Do not post any illegal content or infringe upon intellectual property rights.
              </p>
              <p className="text-gray-700">
                We reserve the right to suspend or terminate accounts that violate these guidelines.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">7. Intellectual Property</h2>
              <p className="text-gray-700 mb-3">
                All content on Startup Stories, except for user-generated content, is the property of Startup Stories or its licensors.
              </p>
              <p className="text-gray-700">
                Do not copy, distribute, or reproduce content without permission.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold mb-4">8. Changes to the Service</h2>
              <p className="text-gray-700">
                We may update, change, or discontinue features or services at any time without notice.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">9. Limitation of Liability</h2>
              <p className="text-gray-700">
                Startup Stories is provided "as is." We do our best to provide accurate, helpful information, but we do not guarantee results and are not liable for business decisions made using our platform.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">10. Changes to These Terms</h2>
              <p className="text-gray-700">
                We may update these Terms from time to time. If we make significant changes, we'll notify you on the website or by email. Continued use of the service means you accept any changes.
              </p>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">11. Contact Us</h2>
              <p className="text-gray-700 mb-4">
                For any questions about these Terms, email <NAME_EMAIL>.
              </p>
              <p className="text-gray-700">
                Thank you for using Startup Stories! We're excited to have you as part of our community.
              </p>
            </section>
          </div>
        </div>
      </main>
      
      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}
      
      <Footer />
    </div>
  );
};

export default TermsOfService; 