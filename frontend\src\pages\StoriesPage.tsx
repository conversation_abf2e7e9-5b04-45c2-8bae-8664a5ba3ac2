import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ChevronUp, Search, Grid, List, Layout, Heart, Eye, BookOpen, DollarSign } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import UserLogin from "@/components/UserLogin";
import { getPublishedStories, getStoriesByCategory, toggleStoryLike } from '@/services/storyService';
import { getCategories, type Category } from '@/lib/categoryService';
import favoriteService from '@/services/favoriteService';
import { Story, Author } from '@/types';
import { useAuth } from '@/lib/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { ChevronDown, ChevronLeft, ChevronRight, MessageSquare, Bookmark } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import SEO from "@/components/SEO";
import { formatDate } from '@/lib/utils';
import { motion } from 'framer-motion';

// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

// Helper function to get complete image URL for featured images
const getImageUrl = (path: string) => {
  if (!path) return '/images/default-story-cover.jpg';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

const StoriesPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'magazine'>('grid');
  const [stories, setStories] = useState<Story[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    pages: 1,
    total: 0
  });
  const [hasMore, setHasMore] = useState(true);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [favoritedStories, setFavoritedStories] = useState<Set<string>>(new Set());
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const { toast } = useToast();

  // Helper function to determine if a story is liked by the current user
  const isLikedByUser = (story: Story): boolean => {
    // Don't show as liked if we don't have authenticated user data
    if (!isAuthenticated || !user?.id || !story.likedBy) {
      return false;
    }
    
    // Check if the current user's ID is in the likedBy array
    const isLiked = story.likedBy.some(likedUserId => {
      if (!likedUserId) return false;
      // Convert both to strings for comparison to handle ObjectId/string differences
      return likedUserId.toString() === user.id.toString();
    });
    
    return isLiked;
  };

  // Load initial data and handle URL parameters
  useEffect(() => {
    loadCategories();
    
    // Check for category parameter in URL
    const categoryParam = searchParams.get('category');
    if (categoryParam && categoryParam !== selectedCategory) {
      setSelectedCategory(categoryParam);
      loadStories(1, true, categoryParam);
    } else {
      loadStories(1);
    }
  }, []);

  // Check favorites status when user is authenticated and stories are loaded
  useEffect(() => {
    const checkFavoritesStatus = async () => {
      if (isAuthenticated && stories.length > 0) {
        try {
          const favoriteChecks = await Promise.all(
            stories.map(story => favoriteService.isFavorited('story', story._id))
          );
          
          const newFavorites = new Set<string>();
          favoriteChecks.forEach((response, index) => {
            if (response.success && response.data.isFavorited) {
              newFavorites.add(stories[index]._id);
            }
          });
          
          setFavoritedStories(newFavorites);
        } catch (error) {
          console.error('Error checking favorites status:', error);
        }
      }
    };

    checkFavoritesStatus();
  }, [isAuthenticated, stories]);

  // Handle URL parameter changes
  useEffect(() => {
    const categoryParam = searchParams.get('category') || 'all';
    if (categoryParam !== selectedCategory) {
      setSelectedCategory(categoryParam);
      loadStories(1, true, categoryParam);
    }
  }, [searchParams]);

  // Handle search term changes
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (searchTerm !== '') {
        loadStories(1, true, selectedCategory, searchTerm);
      } else {
        loadStories(1, true, selectedCategory, '');
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(debounceTimer);
  }, [searchTerm]);

  // Load categories
  const loadCategories = async () => {
    try {
      const response = await getCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      toast({
        title: "Error",
        description: "Failed to load categories. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Load stories function
  const loadStories = async (page = 1, reset = true, categoryId = selectedCategory, search = searchTerm) => {
    try {
      setIsLoading(true);
      let response;
      
      if (categoryId === 'all') {
        response = await getPublishedStories(page, 12, search);
      } else {
        response = await getStoriesByCategory(categoryId, page, 12, search);
      }

      if (response.success) {
        if (reset) {
          setStories(response.data);
        } else {
          setStories(prev => [...prev, ...response.data]);
        }

        setPagination(response.pagination);
        setHasMore(page < response.pagination.pages);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load stories. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle category change
  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    
    // Update URL parameters
    const newSearchParams = new URLSearchParams(searchParams);
    if (categoryId === 'all') {
      newSearchParams.delete('category');
    } else {
      newSearchParams.set('category', categoryId);
    }
    setSearchParams(newSearchParams);
    
    loadStories(1, true, categoryId);
  };

  // Load more stories
  const loadMoreStories = () => {
    if (hasMore && !isLoading) {
      loadStories(pagination.page + 1, false, selectedCategory, searchTerm);
    }
  };

  // Show button when page is scrolled down
  React.useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Use stories directly since filtering is now done on the server-side
  const filteredStories = stories;

  // Toggle like
  const toggleLike = async (e: React.MouseEvent, storyId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      const response = await toggleStoryLike(storyId);

      if (response.success) {
        setStories(prev => {
          const updated = prev.map(s => {
            if (s._id === storyId) {
              const updatedStory = {
                ...s,
                likes: response.data.likes,
                likedBy: response.data.likedBy
              };
              return updatedStory;
            }
            return s;
          });
          return updated;
        });

        toast({
          title: response.data.isLiked ? "Story liked" : "Story unliked",
          description: response.data.isLiked ? "Added to your liked stories" : "Removed from your liked stories",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Toggle favorite
  const toggleFavorite = async (e: React.MouseEvent, storyId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      const response = await favoriteService.toggleFavorite('story', storyId);

      if (response.success) {
        const newFavorites = new Set(favoritedStories);
        if (response.data.isFavorited) {
          newFavorites.add(storyId);
        } else {
          newFavorites.delete(storyId);
        }
        setFavoritedStories(newFavorites);

        toast({
          title: response.data.isFavorited ? "Story bookmarked" : "Bookmark removed",
          description: response.data.isFavorited ? "Added to your favorites" : "Removed from your favorites",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update bookmark status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get read time estimate
  const getReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.trim().split(/\s+/).length;
    const readTime = Math.ceil(words / wordsPerMinute);
    return `${readTime} min read`;
  };

  // Get author info
  const getAuthorInfo = (author: string | Author) => {
    if (typeof author === 'string') {
      return { 
        name: 'Anonymous', 
        avatar: '/default-avatar.png',
        username: 'anonymous',
        isVerified: false,
        roleTitle: 'Entrepreneur'
      };
    }
    return {
      name: author.name,
      avatar: author.avatar || '/default-avatar.png',
      username: author.username || author.name.toLowerCase().replace(/\s+/g, ''),
      isVerified: author.isVerified,
      roleTitle: author.roleTitle
    };
  };

  const openAuthorProfile = (e: React.MouseEvent, author: string | Author) => {
    e.preventDefault();
    e.stopPropagation();
    const authorInfo = getAuthorInfo(author);
    const url = `/author/@${authorInfo.username}`;
    window.open(url, '_blank');
  };

  // Grid View
  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {filteredStories.map((story) => {
        const authorInfo = getAuthorInfo(story.author);
        // Use the helper function to determine if liked
        const isLiked = isLikedByUser(story);

        return (
          <Link to={`/story/${story.slug || story._id}`} key={story._id} className="group">
            <div className="bg-card rounded-lg overflow-hidden shadow-md transition-shadow hover:shadow-lg h-full flex flex-col border border-border">
              <div className="relative w-full h-48">
                <img
                  src={getImageUrl(story.featuredImage)}
                  alt={story.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-3 left-3 flex flex-col items-start gap-1">
                  <Badge className="">
                    {typeof story.category === 'object' ? story.category.name : story.category}
                  </Badge>
                  {story.isPremium && (
                    <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700">
                      Premium
                    </Badge>
                  )}
                </div>
                <div className="absolute top-3 right-3 flex space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className={`h-8 w-8 bg-background/80 hover:bg-background ${favoritedStories.has(story._id) ? 'text-blue-500' : ''}`}
                    onClick={(e) => toggleFavorite(e, story._id)}
                  >
                    <Bookmark className={`h-4 w-4 ${favoritedStories.has(story._id) ? 'fill-current' : ''}`} />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className={`h-8 w-8 bg-background/80 hover:bg-background ${isLiked ? 'text-red-500' : ''}`}
                    onClick={(e) => toggleLike(e, story._id)}
                  >
                    <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                  </Button>
                </div>
              </div>
              <div className="p-5 flex-grow flex flex-col">
                <h3 className="text-xl font-bold mb-2 group-hover:text-brand-600 transition-colors text-foreground">
                  {story.title}
                </h3>
                <p className="text-muted-foreground mb-4 line-clamp-2 flex-grow">
                  {story.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <div 
                    className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={(e) => openAuthorProfile(e, story.author)}
                  >
                    <div className="w-8 h-8 rounded-full overflow-hidden">
                      <img
                        src={getAvatarUrl(authorInfo.avatar)}
                        alt={authorInfo.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-foreground hover:text-brand-600 transition-colors">
                          {authorInfo.name}
                        </span>
                        {authorInfo.isVerified && (
                          <div className="ml-1">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                              <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                            </svg>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {authorInfo.roleTitle || 'Entrepreneur'}
                      </p>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formatDate(story.createdAt)} · {getReadTime(story.content)}
                  </div>
                </div>
              </div>
              <div className="bg-muted/50 px-5 py-2 text-xs text-muted-foreground flex justify-between">
                <span className="flex items-center space-x-4">
                  <span className="flex items-center space-x-1">
                    <Heart className="h-3 w-3" />
                    <span>{story.likes}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>{story.views}</span>
                  </span>
                </span>
              </div>
            </div>
          </Link>
        );
      })}
    </div>
  );

  // List View
  const ListView = () => (
    <div className="space-y-4">
      {filteredStories.map((story) => {
        const authorInfo = getAuthorInfo(story.author);
        // Use the helper function to determine if liked
        const isLiked = isLikedByUser(story);

        return (
          <div key={story._id} className="bg-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-border">
            <div className="flex flex-col md:flex-row">
              <div className="md:w-1/4">
                <Link to={`/story/${story.slug || story._id}`} target="_blank">
                  <div className="relative">
                    <div className="w-full h-40 md:w-64 md:h-64 overflow-hidden">
                      <img
                        src={getImageUrl(story.featuredImage)}
                        alt={story.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute top-3 left-3 flex flex-col items-start gap-1">
                      <Badge variant="secondary" className="dark:bg-purple-900/20 dark:text-purple-300">
                        {typeof story.category === 'object' ? story.category.name : story.category}
                      </Badge>
                      {story.isPremium && (
                        <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                          Premium
                        </Badge>
                      )}
                    </div>
                  </div>
                </Link>
              </div>
              <div className="p-5 md:w-3/4 flex flex-col">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-xs text-muted-foreground">
                    {getReadTime(story.content)}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatDate(story.createdAt)}
                  </span>
                </div>
                <Link to={`/story/${story.slug || story._id}`} target="_blank">
                  <h3 className="font-bold text-xl mb-2 hover:text-primary transition-colors text-foreground">
                    {story.title}
                  </h3>
                </Link>
                <p className="text-muted-foreground mb-4 flex-grow">
                  {story.excerpt}
                </p>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      {story.views}
                    </div>
                    <div className="flex items-center">
                      <Heart className="h-4 w-4 mr-1" />
                      {story.likes}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-8 w-8 ${favoritedStories.has(story._id) ? 'text-blue-500' : ''}`}
                      onClick={(e) => toggleFavorite(e, story._id)}
                    >
                      <Bookmark className={`h-4 w-4 ${favoritedStories.has(story._id) ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-8 w-8 ${isLiked ? 'text-red-500' : ''}`}
                      onClick={(e) => toggleLike(e, story._id)}
                    >
                      <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                    </Button>
                  </div>
                </div>
                
                <div className="flex justify-between items-center mt-auto">
                  <Link to={`/author/@${authorInfo.username}`} target="_blank" className="flex items-center group">
                    <img 
                      src={getAvatarUrl(authorInfo.avatar)}
                      alt={authorInfo.name} 
                      className="w-8 h-8 rounded-full mr-2"
                    />
                    <div>
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                          {authorInfo.name}
                        </p>
                        {authorInfo.isVerified && (
                          <div className="ml-1">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                              <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                            </svg>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {authorInfo.roleTitle || 'Entrepreneur'}
                      </p>
                    </div>
                  </Link>
                  <Button variant="outline" size="sm" asChild>
                    <Link to={`/story/${story.slug || story._id}`} target="_blank">
                      Read Story <ChevronRight size={16} className="ml-1" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="py-16">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-3 text-foreground">Success Stories</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Discover inspiring founder journeys, startup challenges, and entrepreneurial triumphs from our community.
            </p>
            
            {/* Category Slider */}
            <div className="mt-8 mb-6">
              <div className="flex items-center justify-center">
                <div className="flex items-center space-x-4 overflow-x-auto pb-2 max-w-4xl scrollbar-hide"
                     style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                  <Button
                    variant={selectedCategory === 'all' ? 'default' : 'outline'}
                    className={`whitespace-nowrap ${selectedCategory === 'all' ? 'bg-brand-600 hover:bg-brand-700' : ''}`}
                    onClick={() => handleCategoryChange('all')}
                  >
                    All Categories
                  </Button>
                  {categories.map((category) => (
                    <Button
                      key={category._id}
                      variant={selectedCategory === category._id ? 'default' : 'outline'}
                      className={`whitespace-nowrap ${selectedCategory === category._id ? 'bg-brand-600 hover:bg-brand-700' : ''}`}
                      onClick={() => handleCategoryChange(category._id)}
                    >
                      {category.name}
                      {category.accessLevel === 'Premium' && (
                        <DollarSign className="ml-1 h-3 w-3" />
                      )}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Search and View Mode */}
            <div className="flex flex-col md:flex-row items-center justify-between gap-4 mt-8">
              <div className="relative w-full md:max-w-md">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-5 w-5" />
                <Input
                  placeholder="Search stories..."
                  className="pl-10 bg-background border-border"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">View:</span>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="icon"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid size={18} />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="icon"
                  onClick={() => setViewMode('list')}
                >
                  <List size={18} />
                </Button>
                 
              </div>
            </div>
          </div>

          {/* Story Content */}
          {isLoading && stories.length === 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, index) => (
                <Card key={index} className="overflow-hidden bg-card border-border">
                  <div className="w-full h-48 bg-muted animate-pulse"></div>
                  <CardContent className="p-5">
                    <div className="h-6 bg-muted rounded animate-pulse mb-4"></div>
                    <div className="h-4 bg-muted rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-muted rounded animate-pulse mb-4"></div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-muted rounded-full animate-pulse"></div>
                        <div className="h-4 w-20 bg-muted rounded animate-pulse"></div>
                      </div>
                      <div className="h-4 w-28 bg-muted rounded animate-pulse"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredStories.length === 0 ? (
            <div className="text-center py-16">
              <BookOpen className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground mb-4">
                              {searchTerm ? 'No stories found matching your search.' : 'No stories available yet.'}
            </p>
            {searchTerm && (
              <Button variant="link" onClick={() => {
                setSearchTerm('');
                loadStories(1, true, selectedCategory, '');
              }}>
                Clear search
              </Button>
            )}
            </div>
          ) : (
            <>
              {viewMode === 'grid' && <GridView />}
              {viewMode === 'list' && <ListView />} 

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center mt-12">
                  <Button
                    variant="outline"
                    onClick={loadMoreStories}
                    disabled={isLoading}
                    className="px-8"
                  >
                    {isLoading ? 'Loading...' : 'Load More Stories'}
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </main>

      {/* Login Dialog */}
              <UserLogin
          isOpen={showLoginDialog}
          onClose={() => setShowLoginDialog(false)}
        />

      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}

      <Footer />
    </div>
  );
};

export default StoriesPage;