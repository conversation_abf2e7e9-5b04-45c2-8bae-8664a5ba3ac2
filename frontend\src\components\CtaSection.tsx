import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from 'lucide-react';
import { useAuth } from "@/lib/AuthContext";
import { toast } from "@/components/ui/use-toast";
import UserLogin from "./UserLogin";

const CtaSection = () => {
  const { isAuthenticated } = useAuth();
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  const handleJoinCommunity = () => {
    if (isAuthenticated) {
      toast({
        title: "Welcome!",
        description: "You're already part of the community!",
      });
    } else {
      setShowLoginPopup(true);
    }
  };

  return (
    <>
      <section className="py-10 w-full">
        <div className="w-full max-w-none mx-auto px-4">
          <div className="w-full rounded-3xl p-10 md:p-16 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-xl relative overflow-hidden">
            {/* Background Elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full translate-x-1/2 -translate-y-1/2"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-white/10 rounded-full -translate-x-1/2 translate-y-1/2"></div>
            <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-white/5 rounded-full transform -translate-y-1/2"></div>
            
            <div className="relative z-10 max-w-6xl mx-auto">
              <div className="max-w-2xl mx-auto text-center">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Start Your Founder Journey Today</h2>
                <p className="text-lg md:text-xl opacity-90 mb-8">
                Join a community where real entrepreneurs share their stories, insights, and support. Get inspired, learn, and take the next step for your startup.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-white text-indigo-600 hover:bg-gray-100 shadow-lg" onClick={handleJoinCommunity}>
                    Join Our Community <ArrowRight size={16} className="ml-2" />
                  </Button>
                   
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Login Popup */}
      <UserLogin 
        isOpen={showLoginPopup} 
        onClose={() => setShowLoginPopup(false)} 
        preventScroll={true}
        blurBackground={true}
      />
    </>
  );
};

export default CtaSection;