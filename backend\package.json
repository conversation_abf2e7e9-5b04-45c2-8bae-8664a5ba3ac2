{"name": "founder-story-backend", "version": "1.0.0", "description": "Backend for Startup Stories platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mongodb", "node", "jwt", "auth"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "google-auth-library": "^9.0.0", "jsonwebtoken": "^9.0.1", "mongoose": "^7.4.3", "multer": "^1.4.5-lts.2", "nodemailer": "^7.0.3", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "react-hot-toast": "^2.5.2", "slugify": "^1.6.6", "stripe": "^18.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}