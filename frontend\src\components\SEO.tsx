import { Helmet } from 'react-helmet-async';
import { useEffect } from 'react';

export interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  canonical?: string;
}

const SEO: React.FC<SEOProps> = ({
  title = 'StartupStories.io: Real Startup Stories, Founder Insights & SaaS Case Studies',
  description = 'Discover real startup stories, actionable founder case studies, and SaaS growth lessons. Join StartupStories.io to learn, connect, and grow your business journey.',
  keywords = 'startup stories, founder stories, SaaS case studies, tech startups, entrepreneurship, business inspiration, founder interviews, startup community, startup growth, SaaS growth, build in public, startup tips',
  image = 'https://startupstories.io/og-image.jpg',
  url,
  type = 'website',
  author,
  publishedTime,
  modifiedTime,
  section,
  tags,
  noIndex = false,
  canonical
}) => {
  // Use more reliable site URL detection
  const siteUrl = import.meta.env.VITE_FRONTEND_URL || 
                  (typeof window !== 'undefined' ? `${window.location.protocol}//${window.location.host}` : 'https://startupstories.io');
  
  // Get current URL safely
  const currentUrl = typeof window !== 'undefined' ? window.location.href : siteUrl;
  const finalUrl = url || currentUrl;
  const fullUrl = finalUrl.startsWith('http') ? finalUrl : `${siteUrl}${finalUrl}`;
  const fullImageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`;

  console.log('SEO Component Rendering:', {
    title,
    description: description?.substring(0, 50) + '...',
    type,
    fullUrl,
    fullImageUrl,
    siteUrl
  });

  useEffect(() => {
    console.log('SEO Component mounted with title:', title);
    return () => {
      console.log('SEO Component unmounting');
    };
  }, [title]);

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      {author && <meta name="author" content={author} />}
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex,nofollow" />}
      
      {/* Open Graph Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:site_name" content="StartupStories.io" />
      
      {/* Article specific Open Graph tags */}
      {type === 'article' && (
        <>
          {author && <meta property="article:author" content={author} />}
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          {tags && tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@startupstoriesio" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      
      {/* Additional Meta Tags */}
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Structured Data for Organization */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "StartupStories.io",
          "url": siteUrl,
          "logo": `${siteUrl}/logo.png`,
          "description": "StartupStories.io shares real startup stories, founder insights, and actionable SaaS case studies for entrepreneurs and tech founders.",
          "sameAs": [
            "https://twitter.com/startupstoriesio",
            "https://www.linkedin.com/company/startupstoriesio",
            "https://www.instagram.com/startupstoriesio"
          ]
        })}
      </script>
    </Helmet>
  );
};

export default SEO;