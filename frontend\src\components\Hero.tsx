import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Users, FileText, <PERSON><PERSON><PERSON>, Star } from 'lucide-react';
import { useAuth } from "@/lib/AuthContext";
import { toast } from "@/components/ui/use-toast";
import UserLogin from "./UserLogin";

const Hero = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  const handleJoinCommunity = () => {
    if (isAuthenticated) {
      toast({
        title: "Welcome!",
        description: "You're already part of the community!",
      });
    } else {
      setShowLoginPopup(true);
    }
  };

  const handleExploreStories = () => {
    if (isAuthenticated) {
      navigate('/stories');
    } else {
      setShowLoginPopup(true);
    }
  };

  return (
    <>
      <section className="py-16 md:py-24 relative overflow-hidden bg-background">
        {/* Background Elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute -top-1/4 -right-1/4 w-1/2 h-1/2 bg-brand-100 dark:bg-brand-900/20 rounded-full opacity-70 blur-3xl"></div>
          <div className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-brand-200 dark:bg-brand-800/20 rounded-full opacity-70 blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight mb-6">
            See How Real Entrepreneurs
              <span className="gradient-text block mt-2">Built & Grew Their Tech Startups</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join our community where founders openly share their true stories - the challenges, breakthroughs, and big wins.
            Get inspired, pick up practical lessons, and connect with people building amazing things in tech - just like you.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button size="lg" className="w-full sm:w-auto px-8 py-6 text-base" onClick={handleJoinCommunity}>
                Join Our Community
              </Button>
              <Button variant="outline" size="lg" className="w-full sm:w-auto px-8 py-6 text-base" onClick={handleExploreStories}>
                Explore Success Stories
              </Button>
            </div>
          </div>  
          
          {/* Stats Counters */}
          <div className="mt-16 max-w-5xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
              <div className="bg-card rounded-lg shadow-lg p-6 text-center border border-border">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <Users className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <p className="text-3xl font-bold text-foreground">5,200+</p>
                <p className="text-muted-foreground mt-1">Community Members</p>
              </div>
              
              <div className="bg-card rounded-lg shadow-lg p-6 text-center border border-border">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full">
                    <FileText className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
                <p className="text-3xl font-bold text-foreground">850+</p>
                <p className="text-muted-foreground mt-1">Success Stories</p>
              </div>
              
              <div className="bg-card rounded-lg shadow-lg p-6 text-center border border-border">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-full">
                    <BarChart className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <p className="text-3xl font-bold text-foreground">$100M+</p>
                <p className="text-muted-foreground mt-1">Raised by Members</p>
              </div>
              
              <div className="bg-card rounded-lg shadow-lg p-6 text-center border border-border">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-amber-100 dark:bg-amber-900/20 rounded-full">
                    <Star className="h-8 w-8 text-amber-600 dark:text-amber-400" />
                  </div>
                </div>
                <p className="text-3xl font-bold text-foreground">200+</p>
                <p className="text-muted-foreground mt-1">Premium Case Studies</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Login Popup */}
      <UserLogin 
        isOpen={showLoginPopup} 
        onClose={() => setShowLoginPopup(false)} 
        preventScroll={true}
        blurBackground={true}
      />
    </>
  );
};

export default Hero;
