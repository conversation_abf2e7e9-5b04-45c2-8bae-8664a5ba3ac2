import React, { useEffect } from 'react';

interface AdminThemeWrapperProps {
  children: React.ReactNode;
}

const AdminThemeWrapper: React.FC<AdminThemeWrapperProps> = ({ children }) => {
  useEffect(() => {
    const root = document.documentElement;
    const body = document.body;
    
    // Force light theme styles for admin
    const forceAdminLightTheme = () => {
      // Remove dark class
      root.classList.remove('dark');
      
      // Set CSS custom properties to light theme values
      root.style.setProperty('--background', '0 0% 100%');
      root.style.setProperty('--foreground', '240 10% 3.9%');
      root.style.setProperty('--card', '0 0% 100%');
      root.style.setProperty('--card-foreground', '240 10% 3.9%');
      root.style.setProperty('--popover', '0 0% 100%');
      root.style.setProperty('--popover-foreground', '240 10% 3.9%');
      root.style.setProperty('--primary', '252 56% 57%');
      root.style.setProperty('--primary-foreground', '0 0% 98%');
      root.style.setProperty('--secondary', '240 5.9% 90%');
      root.style.setProperty('--secondary-foreground', '240 5.9% 10%');
      root.style.setProperty('--muted', '240 4.8% 95.9%');
      root.style.setProperty('--muted-foreground', '240 3.8% 46.1%');
      root.style.setProperty('--accent', '240 4.8% 95.9%');
      root.style.setProperty('--accent-foreground', '240 5.9% 10%');
      root.style.setProperty('--destructive', '0 84.2% 60.2%');
      root.style.setProperty('--destructive-foreground', '0 0% 98%');
      root.style.setProperty('--border', '240 5.9% 90%');
      root.style.setProperty('--input', '240 5.9% 90%');
      root.style.setProperty('--ring', '252 56% 57%');
    };

    // Apply light theme immediately
    forceAdminLightTheme();

    // Create a MutationObserver to watch for theme changes and prevent them
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const target = mutation.target as HTMLElement;
          if (target === root && target.classList.contains('dark')) {
            // Remove dark class if it gets added
            target.classList.remove('dark');
            forceAdminLightTheme();
          }
        }
      });
    });

    // Start observing
    observer.observe(root, { 
      attributes: true, 
      attributeFilter: ['class'] 
    });

    // Store original theme for cleanup
    const originalTheme = localStorage.getItem('theme');

    // Cleanup function
    return () => {
      observer.disconnect();
      
      // Reset styles and restore original theme behavior
      if (originalTheme) {
        // Restore CSS custom properties based on original theme
        if (originalTheme === 'dark') {
          root.classList.add('dark');
          // Reset styles to allow normal theme behavior
          root.style.removeProperty('--background');
          root.style.removeProperty('--foreground');
          root.style.removeProperty('--card');
          root.style.removeProperty('--card-foreground');
          root.style.removeProperty('--popover');
          root.style.removeProperty('--popover-foreground');
          root.style.removeProperty('--primary');
          root.style.removeProperty('--primary-foreground');
          root.style.removeProperty('--secondary');
          root.style.removeProperty('--secondary-foreground');
          root.style.removeProperty('--muted');
          root.style.removeProperty('--muted-foreground');
          root.style.removeProperty('--accent');
          root.style.removeProperty('--accent-foreground');
          root.style.removeProperty('--destructive');
          root.style.removeProperty('--destructive-foreground');
          root.style.removeProperty('--border');
          root.style.removeProperty('--input');
          root.style.removeProperty('--ring');
        }
      }
    };
  }, []);

  return (
    <div className="admin-theme-wrapper">
      {children}
    </div>
  );
};

export default AdminThemeWrapper; 