import api from './api';
import { Story } from '@/types';

// Interface for creating/updating story
export interface StoryInput {
  title: string;
  excerpt: string;
  content: string;
  category: string;
  tags: string | string[];
  featuredImage?: string;
}

// Get all published stories (with pagination)
export const getPublishedStories = async (page = 1, limit = 12, search = '') => {
  const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
  const response = await api.get(`/stories?page=${page}&limit=${limit}${searchParam}`);
  return response.data;
};

// Get my stories (both published and drafts)
export const getMyStories = async () => {
  const response = await api.get('/stories/user/mystories');
  return response.data;
};

// Get a single story by ID or slug
export const getStory = async (idOrSlug: string) => {
  const response = await api.get(`/stories/${idOrSlug}`);
  return response.data;
};

// Create a new story (initially saved as draft)
export const createStory = async (storyData: StoryInput) => {
  const response = await api.post('/stories', storyData);
  return response.data;
};

// Update a story
export const updateStory = async (id: string, storyData: Partial<StoryInput>) => {
  const response = await api.put(`/stories/${id}`, storyData);
  return response.data;
};

// Delete a story
export const deleteStory = async (id: string) => {
  const response = await api.delete(`/stories/${id}`);
  return response.data;
};

// Upload a featured image
export const uploadStoryImage = async (image: File) => {
  const formData = new FormData();
  formData.append('image', image);
  
  const response = await api.post('/stories/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

// Submit story for publishing (changes status to 'pending')
export const publishStory = async (id: string) => {
  const response = await api.put(`/stories/${id}/publish`);
  return response.data;
};

// Save story as draft
export const saveDraft = async (id: string) => {
  const response = await api.put(`/stories/${id}/draft`);
  return response.data;
};

// Get stories by category
export const getStoriesByCategory = async (categoryId: string, page = 1, limit = 12, search = '') => {
  const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
  const response = await api.get(`/stories/category/${categoryId}?page=${page}&limit=${limit}${searchParam}`);
  return response.data;
};

// Get stories by tag
export const getStoriesByTag = async (tag: string, page = 1, limit = 10) => {
  const response = await api.get(`/stories/tag/${tag}?page=${page}&limit=${limit}`);
  return response.data;
};

// Get random stories by author (excluding current story)
export const getRandomStoriesByAuthor = async (authorId: string, excludeStoryId?: string, limit = 3) => {
  const params = new URLSearchParams({ limit: limit.toString() });
  if (excludeStoryId) {
    params.append('excludeStoryId', excludeStoryId);
  }
  const response = await api.get(`/stories/author/${authorId}/random?${params}`);
  return response.data;
};

// Like a story
export const likeStory = async (id: string): Promise<{ success: boolean; data: { likes: number; likedBy: string[]; isLiked: boolean } }> => {
  const response = await api.put(`/stories/${id}/like`);
  return response.data;
};

// Unlike a story
export const unlikeStory = async (id: string): Promise<{ success: boolean; data: { likes: number; likedBy: string[]; isLiked: boolean } }> => {
  const response = await api.put(`/stories/${id}/unlike`);
  return response.data;
};

// Toggle like/unlike a story
export const toggleStoryLike = async (id: string): Promise<{ success: boolean; data: { likes: number; likedBy: string[]; isLiked: boolean } }> => {
  const response = await api.put(`/stories/${id}/toggle-like`);
  return response.data;
};

// Get all stories for admin (including pending, draft, rejected)
export const getAdminStories = async (page = 1, limit = 10, status = 'all', category = 'all') => {
  const response = await api.get(`/stories/admin/all?page=${page}&limit=${limit}&status=${status}&category=${category}`);
  return response.data;
};

// Admin approve story
export const approveStoryAdmin = async (id: string) => {
  const response = await api.put(`/stories/${id}/approve`);
  return response.data;
};

// Admin reject story with feedback
export const rejectStoryAdmin = async (id: string, feedback: string) => {
  const response = await api.put(`/stories/${id}/reject`, { feedback });
  return response.data;
}; 