import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { User, X } from 'lucide-react';
import { Label } from "@/components/ui/label";
import { Lock, Mail } from 'lucide-react';
import UserLogin from './UserLogin';
import { useAuth } from '@/lib/AuthContext';
import { useGoogleAuth } from '@/lib/googleAuth';
import { toast } from '@/components/ui/use-toast';
import { useRegisterRecaptcha } from '@/hooks/useRecaptcha';

interface UserRegistrationProps {
  isOpen: boolean;
  onClose: () => void;
  preventScroll?: boolean; // Whether to prevent body scroll when open
  blurBackground?: boolean; // Whether to blur the background
}

const UserRegistration: React.FC<UserRegistrationProps> = ({ 
  isOpen, 
  onClose, 
  preventScroll = false, 
  blurBackground = false 
}) => {
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const { register } = useAuth();
  const { initGoogleLogin } = useGoogleAuth();
  const { executeRecaptcha, isReady: recaptchaReady, error: recaptchaError } = useRegisterRecaptcha();
  const googleButtonRef = useRef<HTMLDivElement>(null);
  const dialogRef = useRef<HTMLDivElement>(null);

  // Handle body scroll prevention
  useEffect(() => {
    if (isOpen && preventScroll) {
      // Store original overflow style
      const originalStyle = window.getComputedStyle(document.body).overflow;
      
      // Prevent scrolling
      document.body.style.overflow = 'hidden';
      
      // Add blur effect to main content if requested
      if (blurBackground) {
        const mainElements = document.querySelectorAll('main, article, .container');
        mainElements.forEach(element => {
          (element as HTMLElement).style.filter = 'blur(3px)';
          (element as HTMLElement).style.transition = 'filter 0.3s ease-in-out';
        });
      }
      
      return () => {
        // Restore original overflow
        document.body.style.overflow = originalStyle;
        
        // Remove blur effect
        if (blurBackground) {
          const mainElements = document.querySelectorAll('main, article, .container');
          mainElements.forEach(element => {
            (element as HTMLElement).style.filter = '';
            (element as HTMLElement).style.transition = '';
          });
        }
      };
    }
  }, [isOpen, preventScroll, blurBackground]);

  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        if (googleButtonRef.current) {
          try {
            initGoogleLogin('google-signup-button');
            
            // Ensure button container is visible
            const container = document.getElementById('google-signup-button');
            if (container) {
              container.style.display = 'flex';
              container.style.minHeight = '40px';
              container.style.justifyContent = 'center';
            }
          } catch (error) {
            console.error('Error initializing Google Sign-In:', error);
          }
        }
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen, initGoogleLogin]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Execute reCAPTCHA verification
      let recaptchaToken = null;
      if (recaptchaReady) {
        recaptchaToken = await executeRecaptcha();
        if (recaptchaError) {
          throw new Error('Security verification failed. Please try again.');
        }
      }
      
      // Generate a username from the name if not provided
      const generatedUsername = username || name.toLowerCase().replace(/\s+/g, '') + Math.floor(Math.random() * 1000);
      
      // Prepare registration data with reCAPTCHA token
      const registrationData = {
        name,
        username: generatedUsername,
        email,
        password,
        recaptchaToken // Include reCAPTCHA token in the request
      };
      
      await register(registrationData);
      toast({
        title: "Registration successful",
        description: "Welcome to Startup Stories!",
      });
      onClose();
    } catch (error: any) {
      console.error('Registration error:', error);
      toast({
        title: "Registration failed",
        description: error?.message || error?.response?.data?.error || "An error occurred during registration. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  if (showLoginPopup) {
    return (
      <UserLogin
        isOpen={true}
        onClose={() => {
          setShowLoginPopup(false);
          onClose();
        }}
        preventScroll={preventScroll}
        blurBackground={blurBackground}
      />
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent ref={dialogRef} className="sm:max-w-md" hideCloseButton>
        <DialogHeader>
          <DialogTitle>Join our community</DialogTitle>
          <DialogDescription>
            Register to unlock full access to all stories and connect with fellow entrepreneurs.
          </DialogDescription>
        </DialogHeader>
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-4 top-4"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
        <form onSubmit={handleSubmit} className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="name">Your Name</Label>
            <div className="relative">
              <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              <Input
                id="name"
                type="text"
                placeholder="John Doe"
                className="pl-10"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <div className="relative">
              <User className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              <Input
                id="username"
                type="text"
                placeholder="johndoe123"
                className="pl-10"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={loading}
              />
            </div>
            <p className="text-xs text-gray-500">Leave blank to auto-generate from your name</p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className="pl-10"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                className="pl-10"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={loading}
                minLength={6}
              />
            </div>
          </div>
          <div className="flex flex-col gap-2 pt-2">
            <Button type="submit" className="w-full" disabled={loading || !recaptchaReady}>
              {loading ? "Registering..." : "Register"}
            </Button>
            
            {/* reCAPTCHA status indicator */}
            {!recaptchaReady && (
              <p className="text-xs text-gray-500 text-center">
                Loading security verification...
              </p>
            )}
            {recaptchaError && (
              <p className="text-xs text-red-500 text-center">
                Security verification unavailable. Please refresh the page.
              </p>
            )}
            
            {/* Or separator */}
            <div className="relative my-2">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">OR</span>
              </div>
            </div>
            
            {/* Google Sign-In Button Container */}
            <div 
              id="google-signup-button" 
              ref={googleButtonRef} 
              className="w-full h-10 flex justify-center items-center"
              style={{ 
                minHeight: '40px', 
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: '1px solid transparent',
                borderRadius: '4px',
              }}
            ></div>
            
            <div className="relative my-2">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">ALREADY HAVE AN ACCOUNT?</span>
              </div>
            </div>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setShowLoginPopup(true)} 
              className="w-full"
              disabled={loading}
            >
              Login
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default UserRegistration;

