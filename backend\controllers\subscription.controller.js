const { stripe, STRIPE_CONFIG } = require('../config/stripe');
const User = require('../models/User.model');
const Subscription = require('../models/Subscription.model');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');
const { sendProfileVerifiedConfirmationEmail, sendPremiumSubscriptionConfirmationEmail } = require('../utils/emailService');

/**
 * @desc    Create checkout session for verification subscription
 * @route   POST /api/v1/subscriptions/checkout
 * @access  Private
 */
exports.createCheckoutSession = asyncHandler(async (req, res, next) => {
  const { plan } = req.body;
  const userId = req.user.id;

  if (!plan || !['monthly', 'yearly'].includes(plan)) {
    return next(new ErrorResponse('Please provide a valid plan (monthly or yearly)', 400));
  }

  // Get the price ID based on the plan
  const priceId = plan === 'monthly'
    ? STRIPE_CONFIG.prices.verifiedProfileMonthly
    : STRIPE_CONFIG.prices.verifiedProfileYearly;

  // Check if user already has an active verification subscription
  const existingSubscription = await Subscription.findOne({
    userId,
    subscriptionType: 'verification',
    status: 'active'
  });

  if (existingSubscription) {
    return next(new ErrorResponse('User already has an active verification subscription', 400));
  }

  // Get user details
  const user = await User.findById(userId);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  // Create or retrieve Stripe customer
  let customer;

  // Check if customer already exists
  const existingCustomers = await stripe.customers.list({
    email: user.email,
    limit: 1
  });

  if (existingCustomers.data.length > 0) {
    customer = existingCustomers.data[0];
  } else {
    // Create new customer
    customer = await stripe.customers.create({
      email: user.email,
      name: user.name,
      metadata: {
        userId: user._id.toString()
      }
    });
  }

  // Create checkout session
  const session = await stripe.checkout.sessions.create({
    customer: customer.id,
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1
      }
    ],
    mode: 'subscription',
    success_url: `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.FRONTEND_URL}/profile`,
    metadata: {
      userId: user._id.toString(),
      plan,
      subscriptionType: 'verification'
    }
  });

  res.status(200).json({
    success: true,
    sessionId: session.id,
    url: session.url
  });
});

/**
 * @desc    Get current user verification subscription
 * @route   GET /api/v1/subscriptions/current
 * @access  Private
 */
exports.getCurrentSubscription = asyncHandler(async (req, res, next) => {
  const userId = req.user.id;

  const subscription = await Subscription.findOne({
    userId,
    subscriptionType: 'verification'
  });

  if (!subscription) {
    return res.status(200).json({
      success: true,
      data: null
    });
  }

  res.status(200).json({
    success: true,
    data: subscription
  });
});

/**
 * @desc    Cancel verification subscription
 * @route   DELETE /api/v1/subscriptions/cancel
 * @access  Private
 */
exports.cancelSubscription = asyncHandler(async (req, res, next) => {
  const userId = req.user.id;

  const subscription = await Subscription.findOne({
    userId,
    subscriptionType: 'verification',
    status: 'active'
  });

  if (!subscription) {
    return next(new ErrorResponse('No active verification subscription found', 404));
  }

  // Cancel at period end
  await stripe.subscriptions.update(subscription.subscriptionId, {
    cancel_at_period_end: true
  });

  // Update local subscription
  subscription.cancelAtPeriodEnd = true;
  await subscription.save();

  res.status(200).json({
    success: true,
    data: subscription
  });
});

/**
 * @desc    Create customer portal session for verification subscription
 * @route   POST /api/v1/subscriptions/portal
 * @access  Private
 */
exports.createPortalSession = asyncHandler(async (req, res, next) => {
  const userId = req.user.id;

  const subscription = await Subscription.findOne({
    userId,
    subscriptionType: 'verification'
  });

  if (!subscription) {
    return next(new ErrorResponse('No verification subscription found', 404));
  }

  try {
    // Create a billing portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: subscription.stripeCustomerId,
      return_url: `${process.env.FRONTEND_URL}/profile`
    });

    res.status(200).json({
      success: true,
      url: portalSession.url
    });
  } catch (error) {
    console.error('Stripe portal session error:', error);

    if (error.message && error.message.includes('No configuration provided')) {
      return next(new ErrorResponse('Stripe customer portal is not configured. Please set up your Stripe customer portal settings in the Stripe dashboard.', 500));
    }

    return next(new ErrorResponse(`Failed to create portal session: ${error.message}`, 500));
  }
});

// Helper function to determine subscription type based on price ID
function getSubscriptionType(priceId) {
  if (priceId === STRIPE_CONFIG.prices.verifiedProfileMonthly ||
    priceId === STRIPE_CONFIG.prices.verifiedProfileYearly) {
    return 'verification';
  } else if (priceId === STRIPE_CONFIG.prices.caseStudyYearly) {
    return 'case-study';
  }
  return null;
}

/**
 * @desc    Verify webhook signature and process subscription webhook
 * @route   POST /api/v1/subscriptions/webhook
 * @access  Public
 */
exports.handleWebhook = asyncHandler(async (req, res, next) => {
  const sig = req.headers['stripe-signature'];

  if (!sig) {
    return res.status(400).json({
      success: false,
      error: 'Stripe signature is missing'
    });
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET
    );

    console.log(`✓ Webhook verified: ${event.type}`);
  } catch (err) {
    console.error(`Webhook signature verification failed:`, err.message);
    return res.status(400).json({
      success: false,
      error: `Webhook signature verification failed: ${err.message}`
    });
  }

  try {
    // Handle the event
    await handleStripeEvent(event);
    console.log(`✓ Successfully processed webhook event: ${event.type}`);

    // Return a success response to acknowledge receipt of the event
    return res.status(200).json({
      success: true,
      received: true,
      type: event.type
    });
  } catch (err) {
    console.error(`Error processing webhook ${event.type}:`, err);
    return res.status(500).json({
      success: false,
      error: `Error processing webhook: ${err.message}`
    });
  }
});

// Helper function to handle different Stripe webhook events
async function handleStripeEvent(event) {
  switch (event.type) {
    case 'checkout.session.completed':
      await handleCheckoutSessionCompleted(event.data.object);
      break;
    case 'customer.subscription.created':
      await handleSubscriptionCreated(event.data.object);
      break;
    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object);
      break;
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object);
      break;
    case 'invoice.paid':
      await handleInvoicePaid(event.data.object);
      break;
    case 'invoice.payment_failed':
      await handleInvoicePaymentFailed(event.data.object);
      break;
    default:
      console.log(`Unhandled event type: ${event.type}`);
  }
}

// Helper function to handle checkout.session.completed event
async function handleCheckoutSessionCompleted(session) {
  try {
    // Extract metadata
    const { userId, plan, subscriptionType } = session.metadata;

    if (!userId) {
      console.error('No userId found in session metadata');
      return;
    }

    // Get subscription details from Stripe
    const subscriptionId = session.subscription;

    // Get subscription from Stripe
    const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Determine subscription type from metadata or price ID
    const priceId = stripeSubscription.items.data[0].price.id;
    const detectedSubscriptionType = subscriptionType || getSubscriptionType(priceId);

    if (!detectedSubscriptionType) {
      console.error('Could not determine subscription type for price ID:', priceId);
      return;
    }

    console.log(`Processing ${detectedSubscriptionType} subscription for user: ${userId}`);

    // Log the timestamp values for debugging
    console.log('Period start timestamp:', stripeSubscription.current_period_start);
    console.log('Period end timestamp:', stripeSubscription.current_period_end);

    // Ensure timestamps are valid numbers before creating Date objects
    const startTimestamp = typeof stripeSubscription.current_period_start === 'number'
      ? stripeSubscription.current_period_start * 1000
      : Date.now();

    const endTimestamp = typeof stripeSubscription.current_period_end === 'number'
      ? stripeSubscription.current_period_end * 1000
      : Date.now() + (365 * 24 * 60 * 60 * 1000); // Default to 1 year

    // Use findOneAndUpdate with upsert: true to safely handle race conditions
    await Subscription.findOneAndUpdate(
      { subscriptionId },
      {
        userId,
        stripeCustomerId: session.customer,
        subscriptionId,
        subscriptionType,
        plan: plan || (stripeSubscription.items.data[0].price.recurring.interval === 'month' ? 'monthly' : 'yearly'),
        status: stripeSubscription.status,
        currentPeriodStart: new Date(startTimestamp),
        currentPeriodEnd: new Date(endTimestamp),
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end
      },
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true
      }
    );

    // Update user verification status only for verification subscriptions
    if (detectedSubscriptionType === 'verification') {
      await User.findByIdAndUpdate(userId, {
        isVerified: true,
        verifiedUntil: new Date(endTimestamp)
      });
    }

    console.log(`✓ ${detectedSubscriptionType} subscription created for user: ${userId}`);
  } catch (error) {
    // Check if it's a duplicate key error
    if (error.code === 11000) {
      console.log('Subscription record already exists (duplicate key), skipping creation');
      return;
    }
    console.error('Error in handleCheckoutSessionCompleted:', error);
    throw error;
  }
}

// Helper function to handle subscription.created event
async function handleSubscriptionCreated(subscription) {
  try {
    // Determine subscription type from price ID
    const priceId = subscription.items.data[0].price.id;
    const subscriptionType = getSubscriptionType(priceId);

    if (!subscriptionType) {
      console.log('Unknown subscription type for price ID:', priceId);
      return;
    }

    // Find the customer
    const customer = await stripe.customers.retrieve(subscription.customer);

    if (!customer || !customer.metadata.userId) {
      console.error('No userId found in customer metadata');
      return;
    }

    console.log(`Processing ${subscriptionType} subscription created for user: ${customer.metadata.userId}`);

    // Log the timestamp values for debugging
    console.log('Period start timestamp:', subscription.current_period_start);
    console.log('Period end timestamp:', subscription.current_period_end);

    // Ensure timestamps are valid numbers before creating Date objects
    const startTimestamp = typeof subscription.current_period_start === 'number'
      ? subscription.current_period_start * 1000
      : Date.now();

    const endTimestamp = typeof subscription.current_period_end === 'number'
      ? subscription.current_period_end * 1000
      : Date.now() + (365 * 24 * 60 * 60 * 1000); // Default to 1 year

    // Use findOneAndUpdate with upsert: true to safely handle race conditions
    await Subscription.findOneAndUpdate(
      { subscriptionId: subscription.id },
      {
        userId: customer.metadata.userId,
        stripeCustomerId: subscription.customer,
        subscriptionId: subscription.id,
        subscriptionType,
        plan: subscription.items.data[0].price.recurring.interval === 'month' ? 'monthly' : 'yearly',
        status: subscription.status,
        currentPeriodStart: new Date(startTimestamp),
        currentPeriodEnd: new Date(endTimestamp),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      },
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true
      }
    );

    // Update user verification status only for verification subscriptions
    if (subscriptionType === 'verification') {
      await User.findByIdAndUpdate(customer.metadata.userId, {
        isVerified: true,
        verifiedUntil: new Date(endTimestamp)
      });
    }

    console.log(`✓ ${subscriptionType} subscription created for user: ${customer.metadata.userId}`);
  } catch (error) {
    if (error.code === 11000) {
      console.log('Subscription record already exists (duplicate key), skipping creation');
      return;
    }
    console.error('Error in handleSubscriptionCreated:', error);
    throw error;
  }
}

// Helper function to handle subscription.updated event
async function handleSubscriptionUpdated(subscription) {
  try {
    // Determine subscription type from price ID
    const priceId = subscription.items.data[0].price.id;
    const subscriptionType = getSubscriptionType(priceId);

    if (!subscriptionType) {
      console.log('Unknown subscription type for price ID:', priceId);
      return;
    }

    console.log(`Processing ${subscriptionType} subscription updated: ${subscription.id}`);

    // Log the timestamp values for debugging
    console.log('Period start timestamp:', subscription.current_period_start);
    console.log('Period end timestamp:', subscription.current_period_end);

    // Ensure timestamps are valid numbers before creating Date objects
    const startTimestamp = typeof subscription.current_period_start === 'number'
      ? subscription.current_period_start * 1000
      : Date.now();

    const endTimestamp = typeof subscription.current_period_end === 'number'
      ? subscription.current_period_end * 1000
      : Date.now() + (365 * 24 * 60 * 60 * 1000); // Default to 1 year

    // Check if subscription exists first
    const existingSubscription = await Subscription.findOne({
      subscriptionId: subscription.id,
      subscriptionType
    });

    if (!existingSubscription) {
      console.log(`${subscriptionType} subscription ${subscription.id} not found for update - creating new record`);

      // Try to find the customer to get the userId
      const customer = await stripe.customers.retrieve(subscription.customer);

      if (!customer || !customer.metadata.userId) {
        console.error('No userId found in customer metadata for new subscription');
        return;
      }

      // Create new subscription
      const newSubscription = new Subscription({
        userId: customer.metadata.userId,
        stripeCustomerId: subscription.customer,
        subscriptionId: subscription.id,
        subscriptionType,
        plan: subscription.items.data[0].price.recurring.interval === 'month' ? 'monthly' : 'yearly',
        status: subscription.status,
        currentPeriodStart: new Date(startTimestamp),
        currentPeriodEnd: new Date(endTimestamp),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      });

      await newSubscription.save();

      // Update user verification status only for verification subscriptions
      if (subscriptionType === 'verification') {
        await User.findByIdAndUpdate(customer.metadata.userId, {
          isVerified: subscription.status === 'active',
          verifiedUntil: subscription.status === 'active' ? new Date(endTimestamp) : null
        });
      }

      return;
    }

    // Update existing subscription
    await Subscription.findOneAndUpdate(
      { subscriptionId: subscription.id, subscriptionType },
      {
        status: subscription.status,
        currentPeriodStart: new Date(startTimestamp),
        currentPeriodEnd: new Date(endTimestamp),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        plan: subscription.items.data[0].price.recurring.interval === 'month' ? 'monthly' : 'yearly',
      },
      { new: true }
    );

    // Find the user
    const user = await User.findById(existingSubscription.userId);

    if (!user) {
      console.error(`User not found for subscription: ${subscription.id}`);
      return;
    }

    // Update user verification status only for verification subscriptions
    if (subscriptionType === 'verification') {
      if (subscription.status === 'active') {
        await User.findByIdAndUpdate(user._id, {
          isVerified: true,
          verifiedUntil: new Date(endTimestamp)
        });
      } else if (['canceled', 'unpaid', 'past_due'].includes(subscription.status)) {
        await User.findByIdAndUpdate(user._id, {
          isVerified: false,
          verifiedUntil: null
        });
      }
    }

    console.log(`✓ ${subscriptionType} subscription updated: ${subscription.id}`);
  } catch (error) {
    console.error('Error in handleSubscriptionUpdated:', error);
    throw error;
  }
}

// Helper function to handle subscription.deleted event
async function handleSubscriptionDeleted(subscription) {
  try {
    // Determine subscription type from price ID
    const priceId = subscription.items.data[0].price.id;
    const subscriptionType = getSubscriptionType(priceId);

    if (!subscriptionType) {
      console.log('Unknown subscription type for price ID:', priceId);
      return;
    }

    console.log(`Processing ${subscriptionType} subscription deleted: ${subscription.id}`);

    const deletedSubscription = await Subscription.findOneAndUpdate(
      { subscriptionId: subscription.id, subscriptionType },
      {
        status: 'canceled',
        cancelAtPeriodEnd: true
      },
      { new: true }
    );

    if (!deletedSubscription) {
      console.log(`${subscriptionType} subscription not found for deletion: ${subscription.id}, might be already deleted`);
      return;
    }

    // Remove verification from user only for verification subscriptions
    if (subscriptionType === 'verification') {
      await User.findByIdAndUpdate(deletedSubscription.userId, {
        isVerified: false,
        verifiedUntil: null
      });
    }

    console.log(`✓ ${subscriptionType} subscription deleted: ${subscription.id}`);
  } catch (error) {
    console.error('Error in handleSubscriptionDeleted:', error);
    throw error;
  }
}

// Helper function to handle invoice.paid event
async function handleInvoicePaid(invoice) {
  try {
    if (!invoice.subscription) {
      console.log('Invoice is not subscription-related, skipping');
      return;
    }

    // Get subscription details from Stripe
    const stripeSubscription = await stripe.subscriptions.retrieve(invoice.subscription);

    // Determine subscription type from price ID
    const priceId = stripeSubscription.items.data[0].price.id;
    const subscriptionType = getSubscriptionType(priceId);

    if (!subscriptionType) {
      console.log('Unknown subscription type for invoice payment, price ID:', priceId);
      return;
    }

    console.log(`Processing ${subscriptionType} subscription payment: ${invoice.id}`);

    const customer = await stripe.customers.retrieve(stripeSubscription.customer);

    if (!customer || !customer.metadata.userId) {
      console.error('No userId found in customer metadata');
      return;
    }

    // Get user information for email
    const user = await User.findById(customer.metadata.userId);
    if (!user) {
      console.error('User not found for subscription payment');
      return;
    }

    // Log the timestamp values for debugging
    console.log('Period start timestamp:', stripeSubscription.current_period_start);
    console.log('Period end timestamp:', stripeSubscription.current_period_end);

    // Ensure timestamps are valid numbers before creating Date objects
    const startTimestamp = typeof stripeSubscription.current_period_start === 'number'
      ? stripeSubscription.current_period_start * 1000
      : Date.now();

    const endTimestamp = typeof stripeSubscription.current_period_end === 'number'
      ? stripeSubscription.current_period_end * 1000
      : Date.now() + (365 * 24 * 60 * 60 * 1000); // Default to 1 year

    // Prepare payment history entry
    const paymentEntry = {
      invoiceId: invoice.id,
      amountPaid: invoice.amount_paid,
      currency: invoice.currency,
      paidAt: new Date(invoice.created * 1000),
      paymentStatus: 'paid',
      receiptUrl: invoice.hosted_invoice_url
    };

    // Find the subscription and add payment history
    const subscription = await Subscription.findOne({
      subscriptionId: invoice.subscription,
      subscriptionType
    });

    let isNewSubscription = false;

    if (subscription) {
      // Add payment to existing history
      subscription.paymentHistory.push(paymentEntry);
      subscription.currentPeriodEnd = new Date(endTimestamp);
      await subscription.save();
    } else {
      // Create new subscription with payment history
      await Subscription.findOneAndUpdate(
        { subscriptionId: invoice.subscription },
        {
          userId: customer.metadata.userId,
          stripeCustomerId: stripeSubscription.customer,
          subscriptionId: stripeSubscription.id,
          subscriptionType,
          plan: stripeSubscription.items.data[0].price.recurring.interval === 'month' ? 'monthly' : 'yearly',
          status: stripeSubscription.status,
          currentPeriodStart: new Date(startTimestamp),
          currentPeriodEnd: new Date(endTimestamp),
          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
          paymentHistory: [paymentEntry]
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      );
      isNewSubscription = true;
    }

    // Update user verification status only for verification subscriptions
    if (subscriptionType === 'verification') {
      await User.findByIdAndUpdate(customer.metadata.userId, {
        isVerified: true,
        verifiedUntil: new Date(endTimestamp)
      });
    }

    // Send subscription confirmation email
    try {
      if (subscriptionType === 'verification') {
        await sendProfileVerifiedConfirmationEmail(user.email, user.name);
      } else if (subscriptionType === 'case-study') {
        await sendPremiumSubscriptionConfirmationEmail(user.email, user.name);
      }
    } catch (emailError) {
      console.error(`${subscriptionType} subscription confirmation email failed to send:`, emailError);
      // Don't fail the subscription process if email fails
    }

    console.log(`✓ ${subscriptionType} subscription payment processed: ${invoice.id}`);
  } catch (error) {
    if (error.code === 11000) {
      console.log('Subscription record already exists (duplicate key), skipping creation');
      return;
    }
    console.error('Error in handleInvoicePaid:', error);
    throw error;
  }
}

// Helper function to handle invoice.payment_failed event
async function handleInvoicePaymentFailed(invoice) {
  try {
    if (!invoice.subscription) {
      console.log('Invoice is not subscription-related, skipping');
      return;
    }

    const stripeSubscription = await stripe.subscriptions.retrieve(invoice.subscription);

    // Determine subscription type from price ID
    const priceId = stripeSubscription.items.data[0].price.id;
    const subscriptionType = getSubscriptionType(priceId);

    if (!subscriptionType) {
      console.log('Unknown subscription type for failed payment, price ID:', priceId);
      return;
    }

    console.log(`Processing ${subscriptionType} subscription payment failure: ${invoice.id}`);

    const subscription = await Subscription.findOne({
      subscriptionId: invoice.subscription,
      subscriptionType
    });

    if (!subscription) {
      console.log(`${subscriptionType} subscription not found for failed invoice: ${invoice.id}`);
      return;
    }

    // Add failed payment to history
    subscription.paymentHistory.push({
      invoiceId: invoice.id,
      amountPaid: 0,
      currency: invoice.currency,
      paidAt: new Date(invoice.created * 1000),
      paymentStatus: 'failed',
      receiptUrl: invoice.hosted_invoice_url
    });

    await subscription.save();

    console.log(`✓ ${subscriptionType} subscription payment failed recorded: ${invoice.id}`);
  } catch (error) {
    console.error('Error in handleInvoicePaymentFailed:', error);
    throw error;
  }
} 