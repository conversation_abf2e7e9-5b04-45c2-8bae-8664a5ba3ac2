const User = require('../models/User.model');
const { OAuth2Client } = require('google-auth-library');
const { sendPasswordResetEmail, sendWelcomeEmail } = require('../utils/emailService');
const crypto = require('crypto');

// Create Google OAuth client
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

/**
 * @desc    Register user
 * @route   POST /api/auth/register
 * @access  Public
 */
exports.register = async (req, res) => {
  try {
    const { name, email, password, username } = req.body;

    // Check if user already exists
    let user = await User.findOne({ email });

    if (user) {
      return res.status(400).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    // Check if username is already taken
    const usernameExists = await User.findOne({ username });
    if (usernameExists) {
      return res.status(400).json({
        success: false,
        error: 'Username is already taken'
      });
    }

    // Create user
    user = await User.create({
      name,
      email,
      username,
      password,
      provider: 'local'
    });

    // Send welcome email (don't wait for it to complete)
    try {
      await sendWelcomeEmail(user.email, user.name);
    } catch (emailError) {
      console.error('Welcome email failed to send:', emailError);
      // Don't fail registration if email fails
    }

    sendTokenResponse(user, 201, res);
  } catch (err) {
    console.error(err);
    
    // Handle validation errors
    if (err.name === 'ValidationError') {
      const messages = Object.values(err.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages[0]
      });
    }

    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * @desc    Login user
 * @route   POST /api/auth/login
 * @access  Public
 */
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if email and password are provided
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Please provide an email and password'
      });
    }

    // Check for user
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Check if user is suspended
    if (user.status === 'suspended') {
      return res.status(403).json({
        success: false,
        error: 'Your account has been suspended. Please contact support for assistance.'
      });
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    sendTokenResponse(user, 200, res);
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * @desc    Login with Google
 * @route   POST /api/auth/google
 * @access  Public
 */

exports.googleLogin = async (req, res) => {
  try {
    const { idToken } = req.body;

    // Verify Google token
    const ticket = await client.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    
    // Check if user exists
    let user = await User.findOne({ email: payload.email });

    if (user) {
      // Check if user is suspended
      if (user.status === 'suspended') {
        return res.status(403).json({
          success: false,
          error: 'Your account has been suspended. Please contact support for assistance.'
        });
      }
      
      // Update Google ID if not already set
      if (!user.googleId) {
        user.googleId = payload.sub;
        user.provider = 'google';
        await user.save();
      }
    } else {
      // Generate a unique username based on email or name
      const baseUsername = payload.email.split('@')[0].replace(/[^a-zA-Z0-9_]/g, '') || 
                           payload.name.replace(/\s+/g, '').toLowerCase();
      
      let username = baseUsername;
      let usernameTaken = true;
      let counter = 1;
      
      // Keep trying with incrementing numbers until we find an available username
      while (usernameTaken) {
        const existingUser = await User.findOne({ username });
        if (!existingUser) {
          usernameTaken = false;
        } else {
          username = `${baseUsername}${counter}`;
          counter++;
        }
      }
      
      // Create new user if not exists
      user = await User.create({
        googleId: payload.sub,
        name: payload.name,
        email: payload.email,
        username,
        avatar: payload.picture,
        provider: 'google'
      });

      // Send welcome email for new Google users (don't wait for it to complete)
      try {
        await sendWelcomeEmail(user.email, user.name);
      } catch (emailError) {
        console.error('Welcome email failed to send:', emailError);
        // Don't fail registration if email fails
      }
    }

    sendTokenResponse(user, 200, res);
  } catch (err) {
    console.error('Google login error:', err);
    res.status(500).json({
      success: false,
      error: 'Server Error with Google Authentication'
    });
  }
};

/**
 * @desc    Get current logged in user
 * @route   GET /api/auth/me
 * @access  Private
 */
exports.getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * @desc    Log user out / clear cookie
 * @route   GET /api/auth/logout
 * @access  Private
 */
exports.logout = (req, res) => {
  res.cookie('userToken', 'none', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true,
    path: '/' // Must match the path set during login
  });

  res.status(200).json({
    success: true,
    data: {}
  });
};

/**
 * @desc    Check if a username is available
 * @route   GET /api/auth/check-username/:username
 * @access  Public
 */
exports.checkUsernameAvailability = async (req, res) => {
  try {
    const { username } = req.params;

    // Check if the username meets the minimum requirements
    if (!username || username.length < 3) {
      return res.status(400).json({
        success: false,
        error: 'Username must be at least 3 characters',
        available: false
      });
    }

    // Check if the username follows the required pattern
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
      return res.status(400).json({
        success: false,
        error: 'Username can only contain letters, numbers, and underscores',
        available: false
      });
    }
    
    // Check if username exists in the database
    const existingUser = await User.findOne({ username });
    
    return res.status(200).json({
      success: true,
      available: !existingUser
    });
  } catch (err) {
    console.error('Error checking username availability:', err);
    res.status(500).json({
      success: false,
      error: 'Server Error',
      available: false
    });
  }
};

/**
 * @desc    Forgot password
 * @route   POST /api/auth/forgot-password
 * @access  Public
 */
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'No user found with that email address'
      });
    }

    // Check if user is registered with Google
    if (user.provider === 'google' && !user.password) {
      return res.status(400).json({
        success: false,
        error: 'This account was created with Google. Please use Google Sign-In to access your account.'
      });
    }

    // Get reset token
    const resetToken = user.generatePasswordResetToken();

    await user.save({ validateBeforeSave: false });

    try {
      await sendPasswordResetEmail(user.email, resetToken);

      res.status(200).json({
        success: true,
        message: 'Password reset email sent successfully'
      });
    } catch (err) {
      console.error(err);
      user.resetPasswordToken = undefined;
      user.resetPasswordExpire = undefined;

      await user.save({ validateBeforeSave: false });

      return res.status(500).json({
        success: false,
        error: 'Email could not be sent'
      });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * @desc    Reset password
 * @route   PUT /api/auth/reset-password/:resettoken
 * @access  Public
 */
exports.resetPassword = async (req, res) => {
  try {
    const { password, confirmPassword } = req.body;

    // Check if passwords match
    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        error: 'Passwords do not match'
      });
    }

    // Get hashed token
    const resetPasswordToken = crypto
      .createHash('sha256')
      .update(req.params.resettoken)
      .digest('hex');

    const user = await User.findOne({
      resetPasswordToken,
      resetPasswordExpire: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or expired reset token'
      });
    }

    // Set new password
    user.password = password;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    user.provider = 'local'; // Ensure user can login with password after reset
    
    await user.save();

    sendTokenResponse(user, 200, res);
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Helper function to get token from model, create cookie and send response
const sendTokenResponse = (user, statusCode, res) => {
  // Create token
  const token = user.getSignedJwtToken();

  // Get cookie expiration time from env or use default (1 day in milliseconds)
  const cookieExpire = process.env.JWT_COOKIE_EXPIRE ? 
    parseInt(process.env.JWT_COOKIE_EXPIRE) : 1;
  
  const options = {
    expires: new Date(
      Date.now() + cookieExpire * 24 * 60 * 60 * 1000
    ),
    httpOnly: true,
    path: '/' // Available for all paths except admin (since admin has its own cookie)
  };

  // Use secure cookies in production
  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  res
    .status(statusCode)
    .cookie('userToken', token, options) // Use a distinct cookie name for user sessions
    .json({
      success: true,
      token,
      user: {
        id: user._id,
        name: user.name,
        username: user.username,
        email: user.email,
        role: user.role,
        avatar: user.avatar
      }
    });
}; 