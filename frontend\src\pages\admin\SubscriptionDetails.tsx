import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { ChevronLeft, User, CreditCard, Calendar, MoreHorizontal, ArrowUp, ArrowDown, RefreshCw, Loader2, Download } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import axios from 'axios';

// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

// Interfaces
interface PaymentHistory {
  _id: string;
  invoiceId: string;
  amountPaid: number;
  currency: string;
  paidAt: string;
  paymentStatus: string;
  receiptUrl?: string;
}

interface User {
  _id: string;
  name: string;
  email: string;
  username?: string;
  avatar?: string;
  bio?: string;
}

interface Subscription {
  _id: string;
  userId: User;
  stripeCustomerId: string;
  subscriptionId: string;
  subscriptionType: 'verification' | 'case-study';
  plan: 'monthly' | 'yearly';
  status: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  paymentHistory: PaymentHistory[];
  createdAt: string;
  updatedAt: string;
}

const SubscriptionDetails = () => {
  const { id } = useParams<{ id: string }>();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<'paidAt' | 'amountPaid'>('paidAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const { toast } = useToast();

  // Fetch subscription data
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/admin/subscriptions/${id}`);
        setSubscription(response.data.data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching subscription details:', error);
        toast({
          title: 'Error',
          description: 'Failed to load subscription data. Please try again.',
          variant: 'destructive'
        });
        setLoading(false);
      }
    };

    if (id) {
      fetchSubscription();
    }
  }, [id, toast]);

  // Refresh subscription data
  const refreshData = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/admin/subscriptions/${id}`);
      setSubscription(response.data.data);
      toast({
        title: 'Success',
        description: 'Subscription data refreshed.',
      });
      setLoading(false);
    } catch (error) {
      console.error('Error refreshing subscription data:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh subscription data.',
        variant: 'destructive'
      });
      setLoading(false);
    }
  };

  // Handle sort toggle
  const handleSort = (field: 'paidAt' | 'amountPaid') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Sort payment history
  const sortedPaymentHistory = subscription?.paymentHistory 
    ? [...subscription.paymentHistory].sort((a, b) => {
        if (sortField === 'paidAt') {
          const dateA = new Date(a.paidAt).getTime();
          const dateB = new Date(b.paidAt).getTime();
          return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
        }
        
        if (sortField === 'amountPaid') {
          return sortDirection === 'asc' ? a.amountPaid - b.amountPaid : b.amountPaid - a.amountPaid;
        }
        
        return 0;
      })
    : [];

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'canceled':
        return <Badge className="bg-amber-100 text-amber-800">Canceled</Badge>;
      case 'past_due':
        return <Badge className="bg-red-100 text-red-800">Past Due</Badge>;
      case 'incomplete':
        return <Badge className="bg-gray-100 text-gray-800">Incomplete</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  // Get subscription type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'verification':
        return <Badge className="bg-blue-100 text-blue-800">Verification</Badge>;
      case 'case-study':
        return <Badge className="bg-purple-100 text-purple-800">Case Study</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{type || 'Unknown'}</Badge>;
    }
  };

  // Get payment status badge
  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Link to="/admin/subscriptions">
              <Button variant="outline" size="icon">
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-3xl font-bold">Subscription Details</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={refreshData}
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {subscription?.userId && (
                  <DropdownMenuItem asChild>
                    <Link to={`/admin/users/${subscription.userId._id}`}>
                      View User Profile
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem asChild>
                  <a
                    href={`https://dashboard.stripe.com/customers/${subscription?.stripeCustomerId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    View in Stripe Dashboard
                  </a>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading subscription data...</span>
          </div>
        ) : !subscription ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center h-40">
              <p className="text-gray-500">Subscription not found</p>
              <Button asChild className="mt-4">
                <Link to="/admin/subscriptions">
                  Back to Subscriptions
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* User Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    User Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {subscription.userId.avatar && (
                      <div className="flex justify-center mb-4">
                        <img 
                          src={getAvatarUrl(subscription.userId.avatar)} 
                          alt={subscription.userId.name} 
                          className="h-20 w-20 rounded-full object-cover"
                        />
                      </div>
                    )}
                    
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium">{subscription.userId.name}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{subscription.userId.email}</p>
                    </div>
                    
                    {subscription.userId.username && (
                      <div>
                        <p className="text-sm text-gray-500">Username</p>
                        <p className="font-medium">{subscription.userId.username}</p>
                      </div>
                    )}
                    
                    <div className="pt-2">
                      <Button asChild variant="outline" className="w-full">
                        <Link to={`/admin/users/${subscription.userId._id}`}>
                          View Full Profile
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Subscription Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2" />
                    Subscription Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-500">Status</p>
                      {getStatusBadge(subscription.status)}
                    </div>

                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-500">Type</p>
                      {getTypeBadge(subscription.subscriptionType || 'unknown')}
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Plan</p>
                      <p className="font-medium capitalize">{subscription.plan} Plan</p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Subscription ID</p>
                      <p className="font-mono text-sm">{subscription.subscriptionId}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Stripe Customer ID</p>
                      <p className="font-mono text-sm">{subscription.stripeCustomerId}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Auto-Renew</p>
                      <p className="font-medium">{subscription.cancelAtPeriodEnd ? 'No' : 'Yes'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Billing Period Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Billing Period
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500">Current Period Start</p>
                      <p className="font-medium">
                        {format(new Date(subscription.currentPeriodStart), 'PPP')}
                      </p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Current Period End</p>
                      <p className="font-medium">
                        {format(new Date(subscription.currentPeriodEnd), 'PPP')}
                      </p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Created At</p>
                      <p className="font-medium">
                        {format(new Date(subscription.createdAt), 'PPP')}
                      </p>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-500">Last Updated</p>
                      <p className="font-medium">
                        {format(new Date(subscription.updatedAt), 'PPP')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Payment History */}
            <Tabs defaultValue="payment-history">
              <TabsList>
                <TabsTrigger value="payment-history">Payment History</TabsTrigger>
              </TabsList>
              
              <TabsContent value="payment-history" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Payment History</CardTitle>
                    <CardDescription>
                      View all payment transactions for this subscription
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {sortedPaymentHistory.length === 0 ? (
                      <div className="flex justify-center items-center py-8">
                        <p className="text-gray-500">No payment history found</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Invoice ID</TableHead>
                              <TableHead 
                                className="cursor-pointer"
                                onClick={() => handleSort('amountPaid')}
                              >
                                Amount
                                {sortField === 'amountPaid' && (
                                  sortDirection === 'asc' ? 
                                    <ArrowUp className="inline ml-1 h-4 w-4" /> : 
                                    <ArrowDown className="inline ml-1 h-4 w-4" />
                                )}
                              </TableHead>
                              <TableHead 
                                className="cursor-pointer"
                                onClick={() => handleSort('paidAt')}
                              >
                                Date
                                {sortField === 'paidAt' && (
                                  sortDirection === 'asc' ? 
                                    <ArrowUp className="inline ml-1 h-4 w-4" /> : 
                                    <ArrowDown className="inline ml-1 h-4 w-4" />
                                )}
                              </TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Invoice</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {sortedPaymentHistory.map((payment) => (
                              <TableRow key={payment._id}>
                                <TableCell className="font-mono text-sm">
                                  {payment.invoiceId}
                                </TableCell>
                                <TableCell>
                                  ${payment.amountPaid.toFixed(2)} {payment.currency.toUpperCase()}
                                </TableCell>
                                <TableCell>
                                  {format(new Date(payment.paidAt), 'dd/MM/yyyy HH:mm')}
                                </TableCell>
                                <TableCell>
                                  {getPaymentStatusBadge(payment.paymentStatus)}
                                </TableCell>
                                <TableCell>
                                  {payment.receiptUrl ? (
                                    <a
                                      href={payment.receiptUrl}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-primary hover:underline"
                                    >
                                      <Download  size={22} className="ml-2" />
                                    </a>
                                  ) : (
                                    'N/A'
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default SubscriptionDetails; 